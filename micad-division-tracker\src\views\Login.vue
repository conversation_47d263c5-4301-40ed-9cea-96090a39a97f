<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-card">
        <!-- Header -->
        <div class="login-header">
          <div class="logo">
            <i class="fas fa-water"></i>
          </div>
          <h1 class="title">MICAD Division Hisar</h1>
          <p class="subtitle">Project & Application Tracker</p>
        </div>

        <!-- Login Form -->
        <div class="login-form">
          <form @submit.prevent="handleLogin">
            <div class="form-group">
              <label>Username</label>
              <input
                type="text"
                class="form-input"
                v-model="username"
                placeholder="Enter your username"
                required
                :disabled="loading"
              />
            </div>

            <div class="form-group">
              <label>Password</label>
              <input
                type="password"
                class="form-input"
                v-model="password"
                placeholder="Enter your password"
                required
                :disabled="loading"
              />
            </div>

            <div v-if="error" class="error-message">
              {{ error }}
            </div>

            <button type="submit" class="login-button" :disabled="loading">
              <span v-if="loading">Signing In...</span>
              <span v-else>Sign In</span>
            </button>
          </form>

          <!-- Demo Credentials -->
          <div class="demo-section">
            <h3>Demo Credentials</h3>
            <div class="credentials">
              <div class="credential">
                <strong>Xen (Admin):</strong> xen_hisar / admin123
              </div>
              <div class="credential">
                <strong>Barwala SDO:</strong> barwala_sdo / barwala123
              </div>
              <div class="credential">
                <strong>CAD-1 Hisar:</strong> cad1_hisar_sdo / cad1123
              </div>
              <div class="credential">
                <strong>CAD-2 Hisar:</strong> cad2_hisar_sdo / cad2123
              </div>
              <div class="credential">
                <strong>Narwana SDO:</strong> narwana_sdo / narwana123
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const username = ref('')
const password = ref('')
const loading = ref(false)
const error = ref('')

const handleLogin = async () => {
  if (!username.value || !password.value) {
    error.value = 'Please enter both username and password'
    return
  }

  loading.value = true
  error.value = ''

  try {
    const result = await authStore.login(username.value, password.value)
    
    if (result.success) {
      router.push('/dashboard')
    } else {
      error.value = result.error || 'Login failed'
    }
  } catch (err) {
    error.value = 'An unexpected error occurred'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 900px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 60px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 2rem;
  color: white;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  font-size: 1.2rem;
  color: #718096;
  margin: 0;
}

.login-form {
  margin-bottom: 40px;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
  font-size: 1.1rem;
}

.form-input {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

.login-button {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.error-message {
  background: #fed7d7;
  color: #c53030;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #e53e3e;
}

.demo-section {
  border-top: 2px solid #e2e8f0;
  padding-top: 30px;
}

.demo-section h3 {
  color: #667eea;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
}

.credentials {
  display: grid;
  gap: 12px;
}

.credential {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  border: 1px solid rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  padding: 15px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.credential:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.credential strong {
  color: #2d3748;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-card {
    padding: 40px 30px;
  }

  .title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .form-input {
    padding: 12px 15px;
    font-size: 1rem;
  }

  .login-button {
    padding: 12px;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .login-page {
    padding: 10px;
  }

  .login-card {
    padding: 30px 20px;
  }

  .title {
    font-size: 1.8rem;
  }
}


</style>
