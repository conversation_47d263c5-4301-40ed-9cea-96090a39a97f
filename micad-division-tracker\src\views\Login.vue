<template>
  <div class="login-container">
    <!-- Animated background -->
    <div class="animated-bg">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
    </div>

    <div class="container-fluid h-100">
      <div class="row h-100 justify-content-center align-items-center">
        <div class="col-sm-10 col-md-8 col-lg-6 col-xl-5 col-xxl-4">
          <div class="login-card">
            <!-- Logo and Header -->
            <div class="login-header text-center mb-4">
              <div class="logo-container">
                <div class="logo-circle">
                  <i class="fas fa-water"></i>
                </div>
              </div>
              <h2 class="login-title">MICAD Division Hisar</h2>
              <p class="login-subtitle">Project & Application Tracker</p>
            </div>

            <!-- Login Form -->
            <div class="login-form">
              <form @submit.prevent="handleLogin" class="needs-validation" novalidate>
                <div class="form-floating mb-3">
                  <input
                    type="text"
                    class="form-control modern-input"
                    id="username"
                    v-model="username"
                    placeholder="Enter your username"
                    required
                    :disabled="loading"
                  />
                  <label for="username">
                    <i class="fas fa-user me-2"></i>Username
                  </label>
                </div>

                <div class="form-floating mb-3">
                  <input
                    type="password"
                    class="form-control modern-input"
                    id="password"
                    v-model="password"
                    placeholder="Enter your password"
                    required
                    :disabled="loading"
                  />
                  <label for="password">
                    <i class="fas fa-lock me-2"></i>Password
                  </label>
                </div>

                <div v-if="error" class="alert alert-danger modern-alert mb-3" role="alert">
                  <i class="fas fa-exclamation-triangle me-2"></i>
                  {{ error }}
                </div>

                <button
                  type="submit"
                  class="btn btn-gradient w-100 login-btn"
                  :disabled="loading"
                >
                  <span v-if="loading" class="spinner-border spinner-border-sm me-2" role="status"></span>
                  <i v-else class="fas fa-sign-in-alt me-2"></i>
                  {{ loading ? 'Signing In...' : 'Sign In' }}
                </button>
              </form>

              <!-- Demo Credentials -->
              <div class="demo-credentials">
                <div class="demo-header">
                  <i class="fas fa-info-circle me-2"></i>
                  Demo Credentials
                </div>
                <div class="credentials-grid">
                  <div class="credential-item admin">
                    <div class="credential-role">Xen (Admin)</div>
                    <div class="credential-details">xen_hisar / admin123</div>
                  </div>
                  <div class="credential-item">
                    <div class="credential-role">Barwala SDO</div>
                    <div class="credential-details">barwala_sdo / barwala123</div>
                  </div>
                  <div class="credential-item">
                    <div class="credential-role">CAD-1 Hisar</div>
                    <div class="credential-details">cad1_hisar_sdo / cad1123</div>
                  </div>
                  <div class="credential-item">
                    <div class="credential-role">CAD-2 Hisar</div>
                    <div class="credential-details">cad2_hisar_sdo / cad2123</div>
                  </div>
                  <div class="credential-item">
                    <div class="credential-role">Narwana SDO</div>
                    <div class="credential-details">narwana_sdo / narwana123</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const username = ref('')
const password = ref('')
const loading = ref(false)
const error = ref('')

const handleLogin = async () => {
  if (!username.value || !password.value) {
    error.value = 'Please enter both username and password'
    return
  }

  loading.value = true
  error.value = ''

  try {
    const result = await authStore.login(username.value, password.value)
    
    if (result.success) {
      router.push('/dashboard')
    } else {
      error.value = result.error || 'Login failed'
    }
  } catch (err) {
    error.value = 'An unexpected error occurred'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Animated Background */
.animated-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Floating Shapes */
.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  animation: float 20s infinite linear;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 70%;
  left: 80%;
  animation-delay: 5s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  top: 30%;
  left: 70%;
  animation-delay: 10s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 80%;
  left: 20%;
  animation-delay: 15s;
}

.shape-5 {
  width: 140px;
  height: 140px;
  top: 20%;
  left: 90%;
  animation-delay: 8s;
}

@keyframes float {
  0% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
  33% { transform: translateY(-30px) rotate(120deg); opacity: 0.4; }
  66% { transform: translateY(30px) rotate(240deg); opacity: 0.7; }
  100% { transform: translateY(0px) rotate(360deg); opacity: 0.7; }
}

/* Login Card */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 2.5rem 3rem;
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 10;
  animation: slideInUp 0.8s ease-out;
  max-width: 100%;
  width: 100%;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo and Header */
.login-header {
  margin-bottom: 1.5rem;
}

.logo-container {
  margin-bottom: 1rem;
}

.logo-circle {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.logo-circle i {
  font-size: 1.8rem;
  color: white;
}

.login-title {
  color: #2d3748;
  font-weight: 700;
  font-size: 1.6rem;
  margin-bottom: 0.3rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  color: #718096;
  font-weight: 500;
  font-size: 0.95rem;
  margin: 0;
}

/* Modern Form Inputs */
.form-floating {
  position: relative;
  margin-bottom: 1rem;
}

.modern-input {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 2px solid rgba(102, 126, 234, 0.2) !important;
  border-radius: 12px !important;
  padding: 0.9rem 1.1rem !important;
  font-size: 0.95rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(10px) !important;
  height: 50px !important;
}

.modern-input:focus {
  background: white !important;
  border-color: #667eea !important;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1) !important;
  transform: translateY(-2px) !important;
}

.form-floating label {
  color: #718096 !important;
  font-weight: 500 !important;
  padding: 0 1.2rem !important;
}

.form-floating .modern-input:focus ~ label,
.form-floating .modern-input:not(:placeholder-shown) ~ label {
  color: #667eea !important;
  font-weight: 600 !important;
}

/* Login Button */
.login-btn {
  padding: 0.8rem 2rem !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  border-radius: 12px !important;
  margin-top: 0.5rem !important;
  position: relative !important;
  overflow: hidden !important;
  height: 50px !important;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.login-btn:hover::before {
  left: 100%;
}

/* Modern Alert */
.modern-alert {
  background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%) !important;
  border: none !important;
  border-radius: 15px !important;
  color: #c53030 !important;
  border-left: 4px solid #e53e3e !important;
  backdrop-filter: blur(10px) !important;
}

/* Demo Credentials */
.demo-credentials {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 2px solid rgba(102, 126, 234, 0.1);
}

.demo-header {
  color: #667eea;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.8rem;
  text-align: center;
}

.credentials-grid {
  display: grid;
  gap: 0.5rem;
}

.credential-item {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  border: 1px solid rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  padding: 0.6rem 0.8rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.credential-item:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.credential-item.admin {
  background: linear-gradient(135deg, rgba(245, 101, 101, 0.05) 0%, rgba(245, 101, 101, 0.1) 100%);
  border-color: rgba(245, 101, 101, 0.2);
}

.credential-item.admin:hover {
  background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.15) 100%);
  box-shadow: 0 5px 15px rgba(245, 101, 101, 0.2);
}

.credential-role {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.8rem;
  margin-bottom: 0.2rem;
}

.credential-details {
  font-family: 'Courier New', monospace;
  color: #667eea;
  font-size: 0.75rem;
  font-weight: 500;
}

.credential-item.admin .credential-details {
  color: #e53e3e;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-card {
    padding: 2rem 1.5rem;
    margin: 1rem;
    border-radius: 20px;
  }

  .logo-circle {
    width: 60px;
    height: 60px;
  }

  .logo-circle i {
    font-size: 1.5rem;
  }

  .login-title {
    font-size: 1.4rem;
  }

  .login-subtitle {
    font-size: 0.85rem;
  }

  .modern-input {
    height: 45px !important;
    padding: 0.8rem 1rem !important;
  }

  .login-btn {
    height: 45px !important;
    padding: 0.7rem 1.5rem !important;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 1.5rem 1.2rem;
    margin: 0.5rem;
  }

  .demo-credentials {
    margin-top: 1rem;
    padding-top: 1rem;
  }
}

/* Additional animations */
.login-form {
  animation: fadeIn 1s ease-out 0.3s both;
}

.demo-credentials {
  animation: fadeIn 1s ease-out 0.6s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
