<template>
  <div class="beautiful-login">
    <!-- Animated Background -->
    <div class="background-animation">
      <div class="floating-elements">
        <div class="element element-1"></div>
        <div class="element element-2"></div>
        <div class="element element-3"></div>
        <div class="element element-4"></div>
        <div class="element element-5"></div>
        <div class="element element-6"></div>
      </div>
      <div class="gradient-overlay"></div>
    </div>

    <!-- Main Login Container -->
    <div class="login-wrapper">
      <div class="login-container">
        <!-- Left Side - Branding -->
        <div class="branding-section">
          <div class="brand-content">
            <div class="logo-section">
              <div class="logo-circle">
                <i class="fas fa-water"></i>
              </div>
              <div class="logo-rings">
                <div class="ring ring-1"></div>
                <div class="ring ring-2"></div>
                <div class="ring ring-3"></div>
              </div>
            </div>
            <h1 class="brand-title">MICAD Division</h1>
            <h2 class="brand-subtitle">Hisar</h2>
            <p class="brand-description">Project & Application Tracker</p>
            <div class="brand-features">
              <div class="feature">
                <i class="fas fa-shield-alt"></i>
                <span>Secure Access</span>
              </div>
              <div class="feature">
                <i class="fas fa-chart-line"></i>
                <span>Real-time Tracking</span>
              </div>
              <div class="feature">
                <i class="fas fa-users"></i>
                <span>Multi-user Support</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="form-section">
          <div class="form-container">
            <div class="form-header">
              <h3>Welcome Back</h3>
              <p>Please sign in to your account</p>
            </div>

            <form @submit.prevent="handleLogin" class="login-form">
              <div class="input-group">
                <div class="input-wrapper">
                  <i class="fas fa-user input-icon"></i>
                  <input
                    type="text"
                    class="form-input"
                    v-model="username"
                    placeholder="Username"
                    required
                    :disabled="loading"
                  />
                  <div class="input-border"></div>
                </div>
              </div>

              <div class="input-group">
                <div class="input-wrapper">
                  <i class="fas fa-lock input-icon"></i>
                  <input
                    type="password"
                    class="form-input"
                    v-model="password"
                    placeholder="Password"
                    required
                    :disabled="loading"
                  />
                  <div class="input-border"></div>
                </div>
              </div>

              <div v-if="error" class="error-alert">
                <i class="fas fa-exclamation-triangle"></i>
                <span>{{ error }}</span>
              </div>

              <button type="submit" class="submit-button" :disabled="loading">
                <span v-if="loading" class="loading-content">
                  <i class="fas fa-spinner fa-spin"></i>
                  Signing In...
                </span>
                <span v-else class="normal-content">
                  <i class="fas fa-sign-in-alt"></i>
                  Sign In
                </span>
              </button>
            </form>

            <!-- Demo Credentials -->
            <div class="demo-section">
              <div class="demo-header">
                <i class="fas fa-info-circle"></i>
                <span>Demo Credentials</span>
              </div>
              <div class="demo-grid">
                <div class="demo-card admin-card" @click="fillCredentials('xen_hisar', 'admin123')">
                  <div class="demo-role">Executive Engineer</div>
                  <div class="demo-username">xen_hisar</div>
                  <div class="demo-password">admin123</div>
                </div>
                <div class="demo-card" @click="fillCredentials('barwala_sdo', 'barwala123')">
                  <div class="demo-role">Barwala SDO</div>
                  <div class="demo-username">barwala_sdo</div>
                  <div class="demo-password">barwala123</div>
                </div>
                <div class="demo-card" @click="fillCredentials('cad1_hisar_sdo', 'cad1123')">
                  <div class="demo-role">CAD-1 Hisar</div>
                  <div class="demo-username">cad1_hisar_sdo</div>
                  <div class="demo-password">cad1123</div>
                </div>
                <div class="demo-card" @click="fillCredentials('cad2_hisar_sdo', 'cad2123')">
                  <div class="demo-role">CAD-2 Hisar</div>
                  <div class="demo-username">cad2_hisar_sdo</div>
                  <div class="demo-password">cad2123</div>
                </div>
                <div class="demo-card" @click="fillCredentials('narwana_sdo', 'narwana123')">
                  <div class="demo-role">Narwana SDO</div>
                  <div class="demo-username">narwana_sdo</div>
                  <div class="demo-password">narwana123</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const username = ref('')
const password = ref('')
const loading = ref(false)
const error = ref('')

const handleLogin = async () => {
  if (!username.value || !password.value) {
    error.value = 'Please enter both username and password'
    return
  }

  loading.value = true
  error.value = ''

  try {
    const result = await authStore.login(username.value, password.value)

    if (result.success) {
      router.push('/dashboard')
    } else {
      error.value = result.error || 'Login failed'
    }
  } catch (err) {
    error.value = 'An unexpected error occurred'
  } finally {
    loading.value = false
  }
}

const fillCredentials = (user: string, pass: string) => {
  username.value = user
  password.value = pass
  error.value = ''
}
</script>

<style scoped>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.beautiful-login {
  min-height: 100vh;
  font-family: 'Poppins', sans-serif;
  position: relative;
  overflow: hidden;
}

/* Animated Background */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.element {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  animation: float 20s infinite linear;
}

.element-1 {
  width: 100px;
  height: 100px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.element-2 {
  width: 150px;
  height: 150px;
  top: 70%;
  left: 80%;
  animation-delay: 5s;
}

.element-3 {
  width: 80px;
  height: 80px;
  top: 30%;
  left: 70%;
  animation-delay: 10s;
}

.element-4 {
  width: 120px;
  height: 120px;
  top: 80%;
  left: 20%;
  animation-delay: 15s;
}

.element-5 {
  width: 200px;
  height: 200px;
  top: 20%;
  left: 90%;
  animation-delay: 8s;
}

.element-6 {
  width: 60px;
  height: 60px;
  top: 50%;
  left: 5%;
  animation-delay: 12s;
}

@keyframes float {
  0% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
  33% { transform: translateY(-30px) rotate(120deg); opacity: 0.4; }
  66% { transform: translateY(30px) rotate(240deg); opacity: 0.7; }
  100% { transform: translateY(0px) rotate(360deg); opacity: 0.7; }
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

/* Main Container */
.login-wrapper {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.login-container {
  width: 100%;
  max-width: 1400px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(30px);
  border-radius: 30px;
  box-shadow: 0 30px 80px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: grid;
  grid-template-columns: 1fr 1fr;
  min-height: 700px;
  overflow: hidden;
}

/* Branding Section */
.branding-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.branding-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="90" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.brand-content {
  text-align: center;
  color: white;
  position: relative;
  z-index: 2;
}

.logo-section {
  position: relative;
  margin-bottom: 3rem;
}

.logo-circle {
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  font-size: 3rem;
  color: white;
  position: relative;
  z-index: 3;
  animation: logoFloat 6s ease-in-out infinite;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.logo-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring {
  position: absolute;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring-1 {
  width: 140px;
  height: 140px;
  animation: ringPulse 4s ease-in-out infinite;
}

.ring-2 {
  width: 160px;
  height: 160px;
  animation: ringPulse 4s ease-in-out infinite 1s;
}

.ring-3 {
  width: 180px;
  height: 180px;
  animation: ringPulse 4s ease-in-out infinite 2s;
}

@keyframes ringPulse {
  0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.1; transform: translate(-50%, -50%) scale(1.1); }
}

.brand-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  letter-spacing: -1px;
}

.brand-subtitle {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  opacity: 0.9;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.brand-description {
  font-size: 1.2rem;
  opacity: 0.8;
  margin-bottom: 3rem;
  font-weight: 300;
}

.brand-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.feature {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.feature i {
  font-size: 1.2rem;
}

.feature span {
  font-weight: 500;
}

/* Form Section */
.form-section {
  padding: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-container {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin-bottom: 3rem;
}

.form-header h3 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.form-header p {
  font-size: 1.1rem;
  color: #718096;
  font-weight: 400;
}

.login-form {
  margin-bottom: 3rem;
}

.input-group {
  margin-bottom: 2rem;
}

.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: #667eea;
  font-size: 1.2rem;
  z-index: 2;
  transition: all 0.3s ease;
}

.form-input {
  width: 100%;
  padding: 1.2rem 1.5rem 1.2rem 4rem;
  border: 2px solid #e2e8f0;
  border-radius: 15px;
  font-size: 1.1rem;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
}

.form-input:focus + .input-border {
  transform: scaleX(1);
}

.form-input:focus ~ .input-icon {
  color: #667eea;
  transform: translateY(-50%) scale(1.1);
}

.input-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 0 15px 15px;
  transform: scaleX(0);
  transition: transform 0.3s ease;
  transform-origin: left;
}

.submit-button {
  width: 100%;
  padding: 1.2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 15px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.submit-button:active {
  transform: translateY(-1px);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-content,
.normal-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.error-alert {
  background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
  color: #c53030;
  padding: 1rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  border-left: 4px solid #e53e3e;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Demo Section */
.demo-section {
  border-top: 2px solid #e2e8f0;
  padding-top: 2rem;
}

.demo-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  color: #667eea;
  font-weight: 600;
  font-size: 1.1rem;
}

.demo-grid {
  display: grid;
  gap: 1rem;
}

.demo-card {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  border: 2px solid rgba(102, 126, 234, 0.1);
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.demo-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
  transition: left 0.5s;
}

.demo-card:hover::before {
  left: 100%;
}

.demo-card:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
  border-color: rgba(102, 126, 234, 0.3);
}

.admin-card {
  background: linear-gradient(135deg, rgba(245, 101, 101, 0.05) 0%, rgba(245, 101, 101, 0.1) 100%);
  border-color: rgba(245, 101, 101, 0.2);
}

.admin-card:hover {
  background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.15) 100%);
  box-shadow: 0 8px 25px rgba(245, 101, 101, 0.2);
  border-color: rgba(245, 101, 101, 0.4);
}

.demo-role {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.demo-username {
  font-family: 'Courier New', monospace;
  color: #667eea;
  font-size: 0.85rem;
  font-weight: 500;
  margin-bottom: 0.2rem;
}

.demo-password {
  font-family: 'Courier New', monospace;
  color: #718096;
  font-size: 0.8rem;
  font-weight: 400;
}

.admin-card .demo-username {
  color: #e53e3e;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .login-container {
    max-width: 1000px;
  }

  .branding-section {
    padding: 3rem;
  }

  .form-section {
    padding: 3rem;
  }

  .brand-title {
    font-size: 3rem;
  }

  .brand-subtitle {
    font-size: 2rem;
  }
}

@media (max-width: 992px) {
  .login-container {
    grid-template-columns: 1fr;
    max-width: 600px;
  }

  .branding-section {
    padding: 2rem;
    border-radius: 30px 30px 0 0;
  }

  .form-section {
    padding: 2rem;
    border-radius: 0 0 30px 30px;
  }

  .brand-title {
    font-size: 2.5rem;
  }

  .brand-subtitle {
    font-size: 1.8rem;
  }

  .brand-features {
    flex-direction: row;
    justify-content: space-around;
  }

  .feature {
    flex: 1;
    margin: 0 0.5rem;
  }
}

@media (max-width: 768px) {
  .login-wrapper {
    padding: 1rem;
  }

  .login-container {
    min-height: auto;
  }

  .branding-section {
    padding: 2rem 1.5rem;
  }

  .form-section {
    padding: 2rem 1.5rem;
  }

  .logo-circle {
    width: 100px;
    height: 100px;
    font-size: 2.5rem;
  }

  .ring-1 { width: 120px; height: 120px; }
  .ring-2 { width: 140px; height: 140px; }
  .ring-3 { width: 160px; height: 160px; }

  .brand-title {
    font-size: 2rem;
  }

  .brand-subtitle {
    font-size: 1.5rem;
  }

  .brand-description {
    font-size: 1rem;
  }

  .form-header h3 {
    font-size: 2rem;
  }

  .brand-features {
    flex-direction: column;
  }

  .feature {
    margin: 0;
  }
}

@media (max-width: 480px) {
  .login-wrapper {
    padding: 0.5rem;
  }

  .branding-section {
    padding: 1.5rem 1rem;
  }

  .form-section {
    padding: 1.5rem 1rem;
  }

  .form-container {
    max-width: 100%;
  }

  .logo-circle {
    width: 80px;
    height: 80px;
    font-size: 2rem;
  }

  .brand-title {
    font-size: 1.8rem;
  }

  .brand-subtitle {
    font-size: 1.3rem;
  }

  .form-header h3 {
    font-size: 1.8rem;
  }

  .form-input {
    padding: 1rem 1rem 1rem 3.5rem;
  }

  .input-icon {
    left: 1rem;
    font-size: 1rem;
  }
}

/* Animation Classes */
.login-container {
  animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-container > * {
  animation: fadeInUp 0.6s ease-out;
}

.form-container > *:nth-child(1) { animation-delay: 0.1s; }
.form-container > *:nth-child(2) { animation-delay: 0.2s; }
.form-container > *:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}




</style>
