<template>
  <div class="sidebar" :class="{ 'collapsed': isCollapsed }">
    <div class="sidebar-header">
      <div class="d-flex align-items-center">
        <i class="fas fa-water text-white me-2"></i>
        <h5 v-if="!isCollapsed" class="text-white mb-0">MICAD Hisar</h5>
      </div>
      <button 
        class="btn btn-link text-white p-0 ms-auto"
        @click="toggleSidebar"
      >
        <i class="fas" :class="isCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'"></i>
      </button>
    </div>

    <div class="sidebar-content">
      <nav class="nav flex-column">
        <router-link 
          to="/dashboard" 
          class="nav-link"
          :class="{ active: $route.path === '/dashboard' }"
        >
          <i class="fas fa-tachometer-alt"></i>
          <span v-if="!isCollapsed">Dashboard</span>
        </router-link>

        <router-link 
          to="/applications" 
          class="nav-link"
          :class="{ active: $route.path === '/applications' }"
        >
          <i class="fas fa-file-alt"></i>
          <span v-if="!isCollapsed">Applications</span>
        </router-link>

        <router-link 
          to="/works" 
          class="nav-link"
          :class="{ active: $route.path === '/works' }"
        >
          <i class="fas fa-tools"></i>
          <span v-if="!isCollapsed">Works Status</span>
        </router-link>

        <router-link 
          v-if="authStore.isAdmin"
          to="/users" 
          class="nav-link"
          :class="{ active: $route.path === '/users' }"
        >
          <i class="fas fa-users"></i>
          <span v-if="!isCollapsed">Manage Users</span>
        </router-link>
      </nav>
    </div>

    <div class="sidebar-footer">
      <div v-if="!isCollapsed" class="user-info mb-3">
        <div class="text-white-50 small">Logged in as:</div>
        <div class="text-white fw-bold">{{ authStore.user?.username }}</div>
        <div class="text-white-50 small">
          {{ authStore.isAdmin ? 'Executive Engineer' : `SDO - ${formatSubdivision(authStore.userSubdivision)}` }}
        </div>
      </div>
      
      <button 
        class="btn btn-outline-light w-100"
        @click="handleLogout"
        :title="isCollapsed ? 'Logout' : ''"
      >
        <i class="fas fa-sign-out-alt"></i>
        <span v-if="!isCollapsed" class="ms-2">Logout</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const isCollapsed = ref(false)

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}

const formatSubdivision = (subdivision?: string) => {
  if (!subdivision) return ''
  
  const subdivisionNames: Record<string, string> = {
    'barwala': 'Barwala',
    'cad1_hisar': 'CAD-1 Hisar',
    'cad2_hisar': 'CAD-2 Hisar',
    'narwana': 'Narwana'
  }
  
  return subdivisionNames[subdivision] || subdivision
}
</script>

<style scoped>
.sidebar {
  width: 280px;
  min-height: 100vh;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  transition: width 0.3s ease;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  min-height: 80px;
}

.sidebar-content {
  flex: 1;
  padding: 1rem 0;
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.75rem 1rem;
  margin: 0.25rem 0.5rem;
  border-radius: 8px;
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.nav-link:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.nav-link.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

.nav-link i {
  width: 20px;
  text-align: center;
  margin-right: 0.75rem;
}

.collapsed .nav-link i {
  margin-right: 0;
}

.collapsed .nav-link {
  justify-content: center;
  padding: 0.75rem;
}

.user-info {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.75rem;
  border-radius: 8px;
}

.btn-outline-light {
  border-color: rgba(255, 255, 255, 0.3);
}

.btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    transform: translateX(-100%);
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
}
</style>
