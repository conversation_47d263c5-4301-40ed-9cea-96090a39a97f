<template>
  <div class="sidebar" :class="{ 'collapsed': isCollapsed }">
    <div class="sidebar-header">
      <div class="d-flex align-items-center">
        <i class="fas fa-water text-white me-2"></i>
        <h5 v-if="!isCollapsed" class="text-white mb-0">MICAD Hisar</h5>
      </div>
      <button 
        class="btn btn-link text-white p-0 ms-auto"
        @click="toggleSidebar"
      >
        <i class="fas" :class="isCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'"></i>
      </button>
    </div>

    <div class="sidebar-content">
      <nav class="nav flex-column">
        <router-link 
          to="/dashboard" 
          class="nav-link"
          :class="{ active: $route.path === '/dashboard' }"
        >
          <i class="fas fa-tachometer-alt"></i>
          <span v-if="!isCollapsed">Dashboard</span>
        </router-link>

        <router-link 
          to="/applications" 
          class="nav-link"
          :class="{ active: $route.path === '/applications' }"
        >
          <i class="fas fa-file-alt"></i>
          <span v-if="!isCollapsed">Applications</span>
        </router-link>

        <router-link 
          to="/works" 
          class="nav-link"
          :class="{ active: $route.path === '/works' }"
        >
          <i class="fas fa-tools"></i>
          <span v-if="!isCollapsed">Works Status</span>
        </router-link>

        <router-link 
          v-if="authStore.isAdmin"
          to="/users" 
          class="nav-link"
          :class="{ active: $route.path === '/users' }"
        >
          <i class="fas fa-users"></i>
          <span v-if="!isCollapsed">Manage Users</span>
        </router-link>
      </nav>
    </div>

    <div class="sidebar-footer">
      <div v-if="!isCollapsed" class="user-info mb-3">
        <div class="text-white-50 small">Logged in as:</div>
        <div class="text-white fw-bold">{{ authStore.user?.username }}</div>
        <div class="text-white-50 small">
          {{ authStore.isAdmin ? 'Executive Engineer' : `SDO - ${formatSubdivision(authStore.userSubdivision)}` }}
        </div>
      </div>
      
      <button 
        class="btn btn-outline-light w-100"
        @click="handleLogout"
        :title="isCollapsed ? 'Logout' : ''"
      >
        <i class="fas fa-sign-out-alt"></i>
        <span v-if="!isCollapsed" class="ms-2">Logout</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const isCollapsed = ref(false)

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}

const formatSubdivision = (subdivision?: string) => {
  if (!subdivision) return ''
  
  const subdivisionNames: Record<string, string> = {
    'barwala': 'Barwala',
    'cad1_hisar': 'CAD-1 Hisar',
    'cad2_hisar': 'CAD-2 Hisar',
    'narwana': 'Narwana'
  }
  
  return subdivisionNames[subdivision] || subdivision
}
</script>

<style scoped>
.sidebar {
  width: 280px;
  min-height: 100vh;
  background: linear-gradient(180deg, #1a202c 0%, #2d3748 50%, #4a5568 100%);
  backdrop-filter: blur(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  min-height: 100px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  position: relative;
}

.sidebar-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.1;
  border-radius: 0 0 20px 0;
}

.sidebar-header h5 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  font-size: 1.2rem;
  margin: 0;
}

.sidebar-header i {
  color: #667eea;
  font-size: 1.5rem;
  text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

.sidebar-header button {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s ease;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-header button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white !important;
  transform: rotate(180deg);
}

.sidebar-content {
  flex: 1;
  padding: 1.5rem 0;
  overflow-y: auto;
}

.sidebar-footer {
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 1rem 1.5rem;
  margin: 0.5rem 1rem;
  border-radius: 15px;
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  font-weight: 500;
  font-size: 0.95rem;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
  transition: left 0.5s ease;
  z-index: -1;
}

.nav-link:hover::before {
  left: 0;
}

.nav-link:hover {
  color: white;
  transform: translateX(8px) scale(1.02);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
}

.nav-link.active {
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-weight: 600;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
  transform: translateX(5px);
}

.nav-link.active::before {
  display: none;
}

.nav-link i {
  width: 24px;
  text-align: center;
  margin-right: 1rem;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.nav-link:hover i {
  transform: scale(1.2);
}

.nav-link.active i {
  color: white;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.collapsed .nav-link i {
  margin-right: 0;
  font-size: 1.3rem;
}

.collapsed .nav-link {
  justify-content: center;
  padding: 1rem;
  margin: 0.5rem 0.75rem;
}

.collapsed .nav-link span {
  display: none;
}

.user-info {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1rem;
  border-radius: 15px;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
}

.user-info .text-white {
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.btn-outline-light {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.btn-outline-light:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-color: rgba(255, 255, 255, 0.6);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.collapsed .user-info {
  display: none;
}

.collapsed .btn-outline-light {
  padding: 0.75rem;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapsed .btn-outline-light span {
  display: none;
}

/* Scrollbar styling */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    transform: translateX(-100%);
    z-index: 1050;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .sidebar.collapsed {
    width: 100%;
  }
}

/* Animation for sidebar items */
.nav-link {
  animation: slideInLeft 0.6s ease-out;
}

.nav-link:nth-child(1) { animation-delay: 0.1s; }
.nav-link:nth-child(2) { animation-delay: 0.2s; }
.nav-link:nth-child(3) { animation-delay: 0.3s; }
.nav-link:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
