<template>
  <div class="dashboard-layout">
    <Sidebar />
    
    <div class="main-content">
      <div class="container-fluid">
        <!-- Header -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h2 class="fw-bold mb-1">
                  <i class="fas fa-tools me-2 text-primary"></i>
                  Status of Works
                </h2>
                <p class="text-muted mb-0">
                  Monitor progress and financial status of ongoing works
                </p>
              </div>
              <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" @click="showBulkUpload = true">
                  <i class="fas fa-upload me-2"></i>
                  Bulk Upload
                </button>
                <button class="btn btn-gradient" @click="showAddForm = true">
                  <i class="fas fa-plus me-2"></i>
                  Add New Work
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Search and Filter Bar -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-md-4">
                    <div class="input-group">
                      <span class="input-group-text">
                        <i class="fas fa-search"></i>
                      </span>
                      <input
                        type="text"
                        class="form-control"
                        placeholder="Search works..."
                        v-model="searchQuery"
                      />
                    </div>
                  </div>
                  <div class="col-md-2">
                    <select class="form-select" v-model="progressFilter">
                      <option value="">All Progress</option>
                      <option value="0-25">0-25%</option>
                      <option value="26-50">26-50%</option>
                      <option value="51-75">51-75%</option>
                      <option value="76-99">76-99%</option>
                      <option value="100">Completed</option>
                    </select>
                  </div>
                  <div class="col-md-2" v-if="authStore.isAdmin">
                    <select class="form-select" v-model="subdivisionFilter">
                      <option value="">All Subdivisions</option>
                      <option value="barwala">Barwala</option>
                      <option value="cad1_hisar">CAD-1 Hisar</option>
                      <option value="cad2_hisar">CAD-2 Hisar</option>
                      <option value="narwana">Narwana</option>
                    </select>
                  </div>
                  <div class="col-md-2">
                    <button class="btn btn-outline-secondary w-100" @click="clearFilters">
                      <i class="fas fa-times me-2"></i>
                      Clear
                    </button>
                  </div>
                  <div class="col-md-2">
                    <button class="btn btn-outline-success w-100" @click="exportData">
                      <i class="fas fa-download me-2"></i>
                      Export
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Works Table -->
        <div class="row">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                  <h5 class="mb-0 fw-bold">
                    Works List
                    <span class="badge bg-primary ms-2">{{ filteredWorks.length }}</span>
                  </h5>
                  <div class="d-flex align-items-center gap-3">
                    <small class="text-muted">
                      Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to 
                      {{ Math.min(currentPage * itemsPerPage, filteredWorks.length) }} 
                      of {{ filteredWorks.length }} entries
                    </small>
                  </div>
                </div>
              </div>

              <div class="card-body p-0">
                <div class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead class="table-light">
                      <tr>
                        <th class="border-0">Water Course</th>
                        <th class="border-0">Work Name</th>
                        <th class="border-0">AA No. & Date</th>
                        <th class="border-0">Contractor</th>
                        <th class="border-0">Completion Date</th>
                        <th class="border-0">Physical Progress</th>
                        <th class="border-0">Financial Expenditure</th>
                        <th v-if="authStore.isAdmin" class="border-0">Subdivision</th>
                        <th class="border-0">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="work in paginatedWorks" :key="work.id">
                        <td class="fw-bold text-primary">{{ work.water_course }}</td>
                        <td>{{ work.work_name }}</td>
                        <td>{{ work.aa_no_date }}</td>
                        <td>{{ work.contractor }}</td>
                        <td>{{ formatDate(work.completion_date) }}</td>
                        <td>
                          <div class="d-flex align-items-center">
                            <div class="progress me-2" style="width: 100px; height: 20px;">
                              <div 
                                class="progress-bar" 
                                :class="getProgressBarClass(work.physical_status_percent)"
                                :style="{ width: work.physical_status_percent + '%' }"
                              >
                              </div>
                            </div>
                            <span class="fw-bold">{{ work.physical_status_percent }}%</span>
                          </div>
                        </td>
                        <td class="fw-bold">₹{{ formatCurrency(work.financial_expenditure) }}</td>
                        <td v-if="authStore.isAdmin">
                          <span class="badge bg-secondary">{{ formatSubdivision(work.subdivision) }}</span>
                        </td>
                        <td>
                          <div class="btn-group" role="group">
                            <button 
                              class="btn btn-sm btn-outline-primary" 
                              @click="viewWork(work)"
                              title="View Details"
                            >
                              <i class="fas fa-eye"></i>
                            </button>
                            <button 
                              class="btn btn-sm btn-outline-success" 
                              @click="editWork(work)"
                              title="Edit"
                            >
                              <i class="fas fa-edit"></i>
                            </button>
                            <button 
                              class="btn btn-sm btn-outline-danger" 
                              @click="deleteWork(work.id)"
                              title="Delete"
                            >
                              <i class="fas fa-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                      <tr v-if="paginatedWorks.length === 0">
                        <td :colspan="authStore.isAdmin ? 9 : 8" class="text-center py-4 text-muted">
                          <i class="fas fa-inbox fa-2x mb-3 d-block"></i>
                          No works found
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <!-- Pagination -->
              <div class="card-footer bg-white border-top" v-if="totalPages > 1">
                <nav>
                  <ul class="pagination justify-content-center mb-0">
                    <li class="page-item" :class="{ disabled: currentPage === 1 }">
                      <button class="page-link" @click="currentPage = 1" :disabled="currentPage === 1">
                        First
                      </button>
                    </li>
                    <li class="page-item" :class="{ disabled: currentPage === 1 }">
                      <button class="page-link" @click="currentPage--" :disabled="currentPage === 1">
                        Previous
                      </button>
                    </li>
                    <li 
                      v-for="page in getPageNumbers()" 
                      :key="page"
                      class="page-item" 
                      :class="{ active: page === currentPage }"
                    >
                      <button class="page-link" @click="currentPage = page">{{ page }}</button>
                    </li>
                    <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                      <button class="page-link" @click="currentPage++" :disabled="currentPage === totalPages">
                        Next
                      </button>
                    </li>
                    <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                      <button class="page-link" @click="currentPage = totalPages" :disabled="currentPage === totalPages">
                        Last
                      </button>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add/Edit Work Modal -->
    <div class="modal fade" :class="{ show: showAddForm }" :style="{ display: showAddForm ? 'block' : 'none' }" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-plus me-2"></i>
              {{ editingWork ? 'Edit Work' : 'Add New Work' }}
            </h5>
            <button type="button" class="btn-close" @click="closeAddForm"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="saveWork">
              <div class="row g-3">
                <div class="col-12">
                  <label class="form-label fw-bold">Water Course Name *</label>
                  <input type="text" class="form-control" v-model="workForm.water_course" required>
                </div>
                <div class="col-12">
                  <label class="form-label fw-bold">Work Name *</label>
                  <input type="text" class="form-control" v-model="workForm.work_name" required>
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">AA No. & Date</label>
                  <input type="text" class="form-control" v-model="workForm.aa_no_date">
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">Contractor</label>
                  <input type="text" class="form-control" v-model="workForm.contractor">
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">Completion Date</label>
                  <input type="date" class="form-control" v-model="workForm.completion_date">
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">Physical Status (%)</label>
                  <input 
                    type="number" 
                    class="form-control" 
                    v-model.number="workForm.physical_status_percent" 
                    min="0" 
                    max="100"
                  >
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">Financial Expenditure (₹)</label>
                  <input 
                    type="number" 
                    class="form-control" 
                    v-model.number="workForm.financial_expenditure" 
                    min="0"
                  >
                </div>
                <div class="col-12">
                  <label class="form-label fw-bold">Remarks</label>
                  <textarea class="form-control" rows="3" v-model="workForm.remarks"></textarea>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="closeAddForm">Cancel</button>
            <button type="button" class="btn btn-gradient" @click="saveWork" :disabled="saving">
              <span v-if="saving" class="spinner-border spinner-border-sm me-2"></span>
              {{ saving ? 'Saving...' : 'Save Work' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Bulk Upload Modal -->
    <div class="modal fade" :class="{ show: showBulkUpload }" :style="{ display: showBulkUpload ? 'block' : 'none' }" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-upload me-2"></i>
              Bulk Upload Works
            </h5>
            <button type="button" class="btn-close" @click="showBulkUpload = false"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <p class="text-muted">Upload an Excel file with works data. Make sure your file follows the correct format.</p>
              <a href="#" @click="downloadSampleFile" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-download me-2"></i>
                Download Sample Excel File
              </a>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Select Excel File</label>
              <input type="file" class="form-control" @change="handleFileUpload" accept=".xlsx,.xls">
            </div>
            <div v-if="uploadError" class="alert alert-danger">
              {{ uploadError }}
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="showBulkUpload = false">Cancel</button>
            <button type="button" class="btn btn-gradient" @click="processBulkUpload" :disabled="!selectedFile || uploading">
              <span v-if="uploading" class="spinner-border spinner-border-sm me-2"></span>
              {{ uploading ? 'Uploading...' : 'Upload Data' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Backdrop -->
    <div v-if="showAddForm || showBulkUpload" class="modal-backdrop fade show"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useDataStore, type Work } from '@/stores/data'
import Sidebar from '@/components/common/Sidebar.vue'
import * as XLSX from 'xlsx'

const authStore = useAuthStore()
const dataStore = useDataStore()

// Reactive data
const searchQuery = ref('')
const progressFilter = ref('')
const subdivisionFilter = ref('')
const currentPage = ref(1)
const itemsPerPage = 10

// Modal states
const showAddForm = ref(false)
const showBulkUpload = ref(false)
const editingWork = ref<Work | null>(null)
const saving = ref(false)
const uploading = ref(false)
const selectedFile = ref<File | null>(null)
const uploadError = ref('')

// Form data
const workForm = ref({
  water_course: '',
  work_name: '',
  aa_no_date: '',
  contractor: '',
  completion_date: '',
  physical_status_percent: 0,
  financial_expenditure: 0,
  remarks: '',
  subdivision: authStore.userSubdivision || 'barwala'
})

// Computed properties
const filteredWorks = computed(() => {
  let works = authStore.isAdmin
    ? dataStore.works
    : dataStore.getWorksBySubdivision(authStore.userSubdivision)

  // Apply filters
  if (subdivisionFilter.value && authStore.isAdmin) {
    works = works.filter(work => work.subdivision === subdivisionFilter.value)
  }

  if (progressFilter.value) {
    if (progressFilter.value === '100') {
      works = works.filter(work => work.physical_status_percent === 100)
    } else {
      const [min, max] = progressFilter.value.split('-').map(Number)
      works = works.filter(work =>
        work.physical_status_percent >= min && work.physical_status_percent <= max
      )
    }
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    works = works.filter(work =>
      work.water_course.toLowerCase().includes(query) ||
      work.work_name.toLowerCase().includes(query) ||
      work.contractor.toLowerCase().includes(query) ||
      work.aa_no_date.toLowerCase().includes(query)
    )
  }

  return works.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
})

const totalPages = computed(() =>
  Math.ceil(filteredWorks.value.length / itemsPerPage)
)

const paginatedWorks = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredWorks.value.slice(start, end)
})

// Methods
const formatSubdivision = (subdivision?: string) => {
  if (!subdivision) return ''

  const subdivisionNames: Record<string, string> = {
    'barwala': 'Barwala',
    'cad1_hisar': 'CAD-1 Hisar',
    'cad2_hisar': 'CAD-2 Hisar',
    'narwana': 'Narwana'
  }

  return subdivisionNames[subdivision] || subdivision
}

const getProgressBarClass = (percentage: number) => {
  if (percentage >= 100) return 'bg-success'
  if (percentage >= 75) return 'bg-info'
  if (percentage >= 50) return 'bg-warning'
  return 'bg-danger'
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN').format(amount)
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('en-IN')
}

const clearFilters = () => {
  searchQuery.value = ''
  progressFilter.value = ''
  subdivisionFilter.value = ''
  currentPage.value = 1
}

const getPageNumbers = () => {
  const pages = []
  const maxVisible = 5

  let start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages.value, start + maxVisible - 1)

  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
}

const resetForm = () => {
  workForm.value = {
    water_course: '',
    work_name: '',
    aa_no_date: '',
    contractor: '',
    completion_date: '',
    physical_status_percent: 0,
    financial_expenditure: 0,
    remarks: '',
    subdivision: authStore.userSubdivision || 'barwala'
  }
}

const closeAddForm = () => {
  showAddForm.value = false
  editingWork.value = null
  resetForm()
}

const viewWork = (work: Work) => {
  // Implement view functionality
  console.log('View work:', work)
}

const editWork = (work: Work) => {
  editingWork.value = work
  workForm.value = { ...work }
  showAddForm.value = true
}

const saveWork = async () => {
  saving.value = true

  try {
    if (editingWork.value) {
      // Update existing work
      dataStore.updateWork(editingWork.value.id, workForm.value)
    } else {
      // Add new work
      dataStore.addWork(workForm.value)
    }

    closeAddForm()
  } catch (error) {
    console.error('Error saving work:', error)
  } finally {
    saving.value = false
  }
}

const deleteWork = (id: string) => {
  if (confirm('Are you sure you want to delete this work?')) {
    dataStore.deleteWork(id)
  }
}

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    selectedFile.value = target.files[0]
    uploadError.value = ''
  }
}

const downloadSampleFile = () => {
  const sampleData = [
    {
      'Water Course': 'XYZ Minor',
      'Work Name': 'Lining of water course',
      'AA No. & Date': '123/W - 15/05/2025',
      'Contractor': 'ABC Constructions',
      'Completion Date': '2025-12-31',
      'Physical Status (%)': 45,
      'Financial Expenditure': 550000,
      'Remarks': 'Work progressing as scheduled.'
    }
  ]

  const ws = XLSX.utils.json_to_sheet(sampleData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, 'Works')
  XLSX.writeFile(wb, 'sample_works.xlsx')
}

const processBulkUpload = async () => {
  if (!selectedFile.value) return

  uploading.value = true
  uploadError.value = ''

  try {
    const data = await selectedFile.value.arrayBuffer()
    const workbook = XLSX.read(data)
    const worksheet = workbook.Sheets[workbook.SheetNames[0]]
    const jsonData = XLSX.utils.sheet_to_json(worksheet)

    const works = jsonData.map((row: any) => ({
      water_course: row['Water Course'] || '',
      work_name: row['Work Name'] || '',
      aa_no_date: row['AA No. & Date'] || '',
      contractor: row['Contractor'] || '',
      completion_date: row['Completion Date'] || '',
      physical_status_percent: Number(row['Physical Status (%)']) || 0,
      financial_expenditure: Number(row['Financial Expenditure']) || 0,
      remarks: row['Remarks'] || '',
      subdivision: authStore.userSubdivision || 'barwala'
    }))

    dataStore.bulkAddWorks(works)
    showBulkUpload.value = false
    selectedFile.value = null
  } catch (error) {
    uploadError.value = 'Error processing file. Please check the format and try again.'
  } finally {
    uploading.value = false
  }
}

const exportData = () => {
  const exportData = filteredWorks.value.map(work => ({
    'Water Course': work.water_course,
    'Work Name': work.work_name,
    'AA No. & Date': work.aa_no_date,
    'Contractor': work.contractor,
    'Completion Date': work.completion_date,
    'Physical Status (%)': work.physical_status_percent,
    'Financial Expenditure': work.financial_expenditure,
    'Remarks': work.remarks,
    'Subdivision': formatSubdivision(work.subdivision),
    'Created Date': new Date(work.created_at).toLocaleDateString('en-IN')
  }))

  const ws = XLSX.utils.json_to_sheet(exportData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, 'Works')
  XLSX.writeFile(wb, `works_${new Date().toISOString().split('T')[0]}.xlsx`)
}
</script>

<style scoped>
.dashboard-layout {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  margin-left: 280px;
  padding: 2rem;
  background-color: #f8f9fa;
  transition: margin-left 0.3s ease;
}

.table th {
  font-weight: 600;
  color: #495057;
  white-space: nowrap;
}

.btn-group .btn {
  border-radius: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.modal.show {
  background-color: rgba(0, 0, 0, 0.5);
}

.progress {
  border-radius: 10px;
}

.progress-bar {
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 600;
}

.pagination .page-link {
  border: none;
  color: #6c757d;
  font-weight: 500;
}

.pagination .page-item.active .page-link {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.pagination .page-link:hover {
  background-color: #e9ecef;
  color: #0d6efd;
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding: 1rem;
  }
}
</style>
