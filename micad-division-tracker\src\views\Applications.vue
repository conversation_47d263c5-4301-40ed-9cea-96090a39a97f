<template>
  <div class="dashboard-layout">
    <Sidebar />
    
    <div class="main-content">
      <div class="container-fluid">
        <!-- Header -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h2 class="fw-bold mb-1">
                  <i class="fas fa-file-alt me-2 text-primary"></i>
                  New Applications
                </h2>
                <p class="text-muted mb-0">
                  Manage farmer applications for water course works
                </p>
              </div>
              <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" @click="showBulkUpload = true">
                  <i class="fas fa-upload me-2"></i>
                  Bulk Upload
                </button>
                <button class="btn btn-gradient" @click="showAddForm = true">
                  <i class="fas fa-plus me-2"></i>
                  Add New Application
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Search and Filter Bar -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-md-4">
                    <div class="input-group">
                      <span class="input-group-text">
                        <i class="fas fa-search"></i>
                      </span>
                      <input
                        type="text"
                        class="form-control"
                        placeholder="Search applications..."
                        v-model="searchQuery"
                      />
                    </div>
                  </div>
                  <div class="col-md-2">
                    <select class="form-select" v-model="statusFilter">
                      <option value="">All Status</option>
                      <option value="Pending">Pending</option>
                      <option value="In Progress">In Progress</option>
                      <option value="Completed">Completed</option>
                      <option value="Rejected">Rejected</option>
                    </select>
                  </div>
                  <div class="col-md-2" v-if="authStore.isAdmin">
                    <select class="form-select" v-model="subdivisionFilter">
                      <option value="">All Subdivisions</option>
                      <option value="barwala">Barwala</option>
                      <option value="cad1_hisar">CAD-1 Hisar</option>
                      <option value="cad2_hisar">CAD-2 Hisar</option>
                      <option value="narwana">Narwana</option>
                    </select>
                  </div>
                  <div class="col-md-2">
                    <button class="btn btn-outline-secondary w-100" @click="clearFilters">
                      <i class="fas fa-times me-2"></i>
                      Clear
                    </button>
                  </div>
                  <div class="col-md-2">
                    <button class="btn btn-outline-success w-100" @click="exportData">
                      <i class="fas fa-download me-2"></i>
                      Export
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Applications Table -->
        <div class="row">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                  <h5 class="mb-0 fw-bold">
                    Applications List
                    <span class="badge bg-primary ms-2">{{ filteredApplications.length }}</span>
                  </h5>
                  <div class="d-flex align-items-center gap-3">
                    <small class="text-muted">
                      Showing {{ ((currentPage - 1) * itemsPerPage) + 1 }} to 
                      {{ Math.min(currentPage * itemsPerPage, filteredApplications.length) }} 
                      of {{ filteredApplications.length }} entries
                    </small>
                  </div>
                </div>
              </div>

              <div class="card-body p-0">
                <div class="table-responsive">
                  <table class="table table-hover mb-0">
                    <thead class="table-light">
                      <tr>
                        <th class="border-0">Sr. No.</th>
                        <th class="border-0">RD</th>
                        <th class="border-0">Water Course</th>
                        <th class="border-0">Village</th>
                        <th class="border-0">Farmer Name</th>
                        <th class="border-0">Contact</th>
                        <th class="border-0">Work Type</th>
                        <th class="border-0">Status</th>
                        <th v-if="authStore.isAdmin" class="border-0">Subdivision</th>
                        <th class="border-0">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr 
                        v-for="application in paginatedApplications" 
                        :key="application.id"
                        :class="getStatusClass(application.status)"
                      >
                        <td>{{ application.sr_no }}</td>
                        <td>{{ application.rd }}</td>
                        <td class="fw-bold text-primary">{{ application.minor }}</td>
                        <td>{{ application.village }}</td>
                        <td>{{ application.farmer_name }}</td>
                        <td>{{ application.contact }}</td>
                        <td>{{ application.work_type }}</td>
                        <td>
                          <span class="badge" :class="getStatusBadgeClass(application.status)">
                            {{ application.status }}
                          </span>
                        </td>
                        <td v-if="authStore.isAdmin">
                          <span class="badge bg-secondary">{{ formatSubdivision(application.subdivision) }}</span>
                        </td>
                        <td>
                          <div class="btn-group" role="group">
                            <button 
                              class="btn btn-sm btn-outline-primary" 
                              @click="viewApplication(application)"
                              title="View Details"
                            >
                              <i class="fas fa-eye"></i>
                            </button>
                            <button 
                              class="btn btn-sm btn-outline-success" 
                              @click="editApplication(application)"
                              title="Edit"
                            >
                              <i class="fas fa-edit"></i>
                            </button>
                            <button 
                              class="btn btn-sm btn-outline-danger" 
                              @click="deleteApplication(application.id)"
                              title="Delete"
                            >
                              <i class="fas fa-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                      <tr v-if="paginatedApplications.length === 0">
                        <td :colspan="authStore.isAdmin ? 10 : 9" class="text-center py-4 text-muted">
                          <i class="fas fa-inbox fa-2x mb-3 d-block"></i>
                          No applications found
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <!-- Pagination -->
              <div class="card-footer bg-white border-top" v-if="totalPages > 1">
                <nav>
                  <ul class="pagination justify-content-center mb-0">
                    <li class="page-item" :class="{ disabled: currentPage === 1 }">
                      <button class="page-link" @click="currentPage = 1" :disabled="currentPage === 1">
                        First
                      </button>
                    </li>
                    <li class="page-item" :class="{ disabled: currentPage === 1 }">
                      <button class="page-link" @click="currentPage--" :disabled="currentPage === 1">
                        Previous
                      </button>
                    </li>
                    <li 
                      v-for="page in getPageNumbers()" 
                      :key="page"
                      class="page-item" 
                      :class="{ active: page === currentPage }"
                    >
                      <button class="page-link" @click="currentPage = page">{{ page }}</button>
                    </li>
                    <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                      <button class="page-link" @click="currentPage++" :disabled="currentPage === totalPages">
                        Next
                      </button>
                    </li>
                    <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                      <button class="page-link" @click="currentPage = totalPages" :disabled="currentPage === totalPages">
                        Last
                      </button>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add/Edit Application Modal -->
    <div class="modal fade" :class="{ show: showAddForm }" :style="{ display: showAddForm ? 'block' : 'none' }" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-plus me-2"></i>
              {{ editingApplication ? 'Edit Application' : 'Add New Application' }}
            </h5>
            <button type="button" class="btn-close" @click="closeAddForm"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="saveApplication">
              <div class="row g-3">
                <div class="col-md-6">
                  <label class="form-label fw-bold">Sr. No. *</label>
                  <input type="text" class="form-control" v-model="applicationForm.sr_no" required>
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">RD *</label>
                  <input type="text" class="form-control" v-model="applicationForm.rd" required>
                </div>
                <div class="col-12">
                  <label class="form-label fw-bold">Water Course Name *</label>
                  <input type="text" class="form-control" v-model="applicationForm.minor" required>
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">Village *</label>
                  <input type="text" class="form-control" v-model="applicationForm.village" required>
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">Farmer Name *</label>
                  <input type="text" class="form-control" v-model="applicationForm.farmer_name" required>
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">Contact Number</label>
                  <input type="tel" class="form-control" v-model="applicationForm.contact">
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">Work Type</label>
                  <select class="form-select" v-model="applicationForm.work_type">
                    <option value="">Select Work Type</option>
                    <option value="New Construction">New Construction</option>
                    <option value="Extension">Extension</option>
                    <option value="Repair">Repair</option>
                    <option value="Maintenance">Maintenance</option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">Status</label>
                  <select class="form-select" v-model="applicationForm.status">
                    <option value="Pending">Pending</option>
                    <option value="In Progress">In Progress</option>
                    <option value="Completed">Completed</option>
                    <option value="Rejected">Rejected</option>
                  </select>
                </div>
                <div class="col-md-6">
                  <label class="form-label fw-bold">Reference</label>
                  <input type="text" class="form-control" v-model="applicationForm.reference">
                </div>
                <div class="col-12">
                  <label class="form-label fw-bold">ATR (Action Taken Report)</label>
                  <textarea class="form-control" rows="3" v-model="applicationForm.atr"></textarea>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="closeAddForm">Cancel</button>
            <button type="button" class="btn btn-gradient" @click="saveApplication" :disabled="saving">
              <span v-if="saving" class="spinner-border spinner-border-sm me-2"></span>
              {{ saving ? 'Saving...' : 'Save Application' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Bulk Upload Modal -->
    <div class="modal fade" :class="{ show: showBulkUpload }" :style="{ display: showBulkUpload ? 'block' : 'none' }" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-upload me-2"></i>
              Bulk Upload Applications
            </h5>
            <button type="button" class="btn-close" @click="showBulkUpload = false"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <p class="text-muted">Upload an Excel file with application data. Make sure your file follows the correct format.</p>
              <a href="#" @click="downloadSampleFile" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-download me-2"></i>
                Download Sample Excel File
              </a>
            </div>
            <div class="mb-3">
              <label class="form-label fw-bold">Select Excel File</label>
              <input type="file" class="form-control" @change="handleFileUpload" accept=".xlsx,.xls">
            </div>
            <div v-if="uploadError" class="alert alert-danger">
              {{ uploadError }}
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" @click="showBulkUpload = false">Cancel</button>
            <button type="button" class="btn btn-gradient" @click="processBulkUpload" :disabled="!selectedFile || uploading">
              <span v-if="uploading" class="spinner-border spinner-border-sm me-2"></span>
              {{ uploading ? 'Uploading...' : 'Upload Data' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Backdrop -->
    <div v-if="showAddForm || showBulkUpload" class="modal-backdrop fade show"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useDataStore, type Application } from '@/stores/data'
import Sidebar from '@/components/common/Sidebar.vue'
import * as XLSX from 'xlsx'

const authStore = useAuthStore()
const dataStore = useDataStore()

// Reactive data
const searchQuery = ref('')
const statusFilter = ref('')
const subdivisionFilter = ref('')
const currentPage = ref(1)
const itemsPerPage = 10

// Modal states
const showAddForm = ref(false)
const showBulkUpload = ref(false)
const editingApplication = ref<Application | null>(null)
const saving = ref(false)
const uploading = ref(false)
const selectedFile = ref<File | null>(null)
const uploadError = ref('')

// Form data
const applicationForm = ref({
  sr_no: '',
  rd: '',
  minor: '',
  village: '',
  status: 'Pending' as const,
  work_type: '',
  farmer_name: '',
  contact: '',
  reference: '',
  atr: '',
  subdivision: authStore.userSubdivision || 'barwala'
})

// Computed properties
const filteredApplications = computed(() => {
  let applications = authStore.isAdmin
    ? dataStore.applications
    : dataStore.getApplicationsBySubdivision(authStore.userSubdivision)

  // Apply filters
  if (subdivisionFilter.value && authStore.isAdmin) {
    applications = applications.filter(app => app.subdivision === subdivisionFilter.value)
  }

  if (statusFilter.value) {
    applications = applications.filter(app => app.status === statusFilter.value)
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    applications = applications.filter(app =>
      app.minor.toLowerCase().includes(query) ||
      app.farmer_name.toLowerCase().includes(query) ||
      app.village.toLowerCase().includes(query) ||
      app.contact.toLowerCase().includes(query) ||
      app.work_type.toLowerCase().includes(query)
    )
  }

  return applications.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
})

const totalPages = computed(() =>
  Math.ceil(filteredApplications.value.length / itemsPerPage)
)

const paginatedApplications = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredApplications.value.slice(start, end)
})

// Methods
const formatSubdivision = (subdivision?: string) => {
  if (!subdivision) return ''

  const subdivisionNames: Record<string, string> = {
    'barwala': 'Barwala',
    'cad1_hisar': 'CAD-1 Hisar',
    'cad2_hisar': 'CAD-2 Hisar',
    'narwana': 'Narwana'
  }

  return subdivisionNames[subdivision] || subdivision
}

const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    'Pending': 'status-pending',
    'In Progress': 'status-in-progress',
    'Completed': 'status-completed',
    'Rejected': 'status-rejected'
  }
  return statusClasses[status] || ''
}

const getStatusBadgeClass = (status: string) => {
  const badgeClasses: Record<string, string> = {
    'Pending': 'bg-warning',
    'In Progress': 'bg-primary',
    'Completed': 'bg-success',
    'Rejected': 'bg-danger'
  }
  return badgeClasses[status] || 'bg-secondary'
}

const clearFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  subdivisionFilter.value = ''
  currentPage.value = 1
}

const getPageNumbers = () => {
  const pages = []
  const maxVisible = 5

  let start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages.value, start + maxVisible - 1)

  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
}

const resetForm = () => {
  applicationForm.value = {
    sr_no: '',
    rd: '',
    minor: '',
    village: '',
    status: 'Pending',
    work_type: '',
    farmer_name: '',
    contact: '',
    reference: '',
    atr: '',
    subdivision: authStore.userSubdivision || 'barwala'
  }
}

const closeAddForm = () => {
  showAddForm.value = false
  editingApplication.value = null
  resetForm()
}

const viewApplication = (application: Application) => {
  // Implement view functionality
  console.log('View application:', application)
}

const editApplication = (application: Application) => {
  editingApplication.value = application
  applicationForm.value = { ...application }
  showAddForm.value = true
}

const saveApplication = async () => {
  saving.value = true

  try {
    if (editingApplication.value) {
      // Update existing application
      dataStore.updateApplication(editingApplication.value.id, applicationForm.value)
    } else {
      // Add new application
      dataStore.addApplication(applicationForm.value)
    }

    closeAddForm()
  } catch (error) {
    console.error('Error saving application:', error)
  } finally {
    saving.value = false
  }
}

const deleteApplication = (id: string) => {
  if (confirm('Are you sure you want to delete this application?')) {
    dataStore.deleteApplication(id)
  }
}

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    selectedFile.value = target.files[0]
    uploadError.value = ''
  }
}

const downloadSampleFile = () => {
  const sampleData = [
    {
      'Sr. No.': '1',
      'RD': '15000',
      'Water Course Name': 'ABC Minor',
      'Village': 'Ramnagar',
      'Farmer Name': 'Ramesh Kumar',
      'Contact': '9876543210',
      'Work Type': 'Extension',
      'Status': 'Pending',
      'Reference': 'MLA-2025-01',
      'ATR': 'JE has inspected the site.'
    }
  ]

  const ws = XLSX.utils.json_to_sheet(sampleData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, 'Applications')
  XLSX.writeFile(wb, 'sample_applications.xlsx')
}

const processBulkUpload = async () => {
  if (!selectedFile.value) return

  uploading.value = true
  uploadError.value = ''

  try {
    const data = await selectedFile.value.arrayBuffer()
    const workbook = XLSX.read(data)
    const worksheet = workbook.Sheets[workbook.SheetNames[0]]
    const jsonData = XLSX.utils.sheet_to_json(worksheet)

    const applications = jsonData.map((row: any) => ({
      sr_no: row['Sr. No.'] || '',
      rd: row['RD'] || '',
      minor: row['Water Course Name'] || '',
      village: row['Village'] || '',
      farmer_name: row['Farmer Name'] || '',
      contact: row['Contact'] || '',
      work_type: row['Work Type'] || '',
      status: row['Status'] || 'Pending',
      reference: row['Reference'] || '',
      atr: row['ATR'] || '',
      subdivision: authStore.userSubdivision || 'barwala'
    }))

    dataStore.bulkAddApplications(applications)
    showBulkUpload.value = false
    selectedFile.value = null
  } catch (error) {
    uploadError.value = 'Error processing file. Please check the format and try again.'
  } finally {
    uploading.value = false
  }
}

const exportData = () => {
  const exportData = filteredApplications.value.map(app => ({
    'Sr. No.': app.sr_no,
    'RD': app.rd,
    'Water Course Name': app.minor,
    'Village': app.village,
    'Farmer Name': app.farmer_name,
    'Contact': app.contact,
    'Work Type': app.work_type,
    'Status': app.status,
    'Reference': app.reference,
    'ATR': app.atr,
    'Subdivision': formatSubdivision(app.subdivision),
    'Created Date': new Date(app.created_at).toLocaleDateString('en-IN')
  }))

  const ws = XLSX.utils.json_to_sheet(exportData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, 'Applications')
  XLSX.writeFile(wb, `applications_${new Date().toISOString().split('T')[0]}.xlsx`)
}
</script>

<style scoped>
.dashboard-layout {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  margin-left: 280px;
  padding: 2rem;
  background-color: #f8f9fa;
  transition: margin-left 0.3s ease;
}

.table th {
  font-weight: 600;
  color: #495057;
  white-space: nowrap;
}

.btn-group .btn {
  border-radius: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.modal.show {
  background-color: rgba(0, 0, 0, 0.5);
}

.pagination .page-link {
  border: none;
  color: #6c757d;
  font-weight: 500;
}

.pagination .page-item.active .page-link {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.pagination .page-link:hover {
  background-color: #e9ecef;
  color: #0d6efd;
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding: 1rem;
  }
}
</style>
