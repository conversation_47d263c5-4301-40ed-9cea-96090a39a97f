<template>
  <div class="dashboard-layout">
    <Sidebar />
    
    <div class="main-content">
      <div class="container-fluid">
        <!-- Header -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h2 class="fw-bold mb-1">
                  <i class="fas fa-users me-2 text-primary"></i>
                  User Management
                </h2>
                <p class="text-muted mb-0">
                  Manage subdivision user accounts and passwords
                </p>
              </div>
              <div class="text-end">
                <div class="text-muted small">Admin Access Only</div>
                <div class="fw-bold text-success">
                  <i class="fas fa-shield-alt me-1"></i>
                  Secure Area
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- User Management Cards -->
        <div class="row">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-white border-bottom">
                <h5 class="mb-0 fw-bold">
                  <i class="fas fa-building me-2"></i>
                  Subdivision User Accounts
                </h5>
              </div>

              <div class="card-body">
                <div class="row g-4">
                  <!-- Barwala -->
                  <div class="col-md-6">
                    <div class="card border border-primary">
                      <div class="card-header bg-primary text-white">
                        <h6 class="mb-0 fw-bold">
                          <i class="fas fa-map-marker-alt me-2"></i>
                          Barwala Subdivision
                        </h6>
                      </div>
                      <div class="card-body">
                        <div class="mb-3">
                          <label class="form-label fw-bold">Username</label>
                          <input 
                            type="text" 
                            class="form-control" 
                            v-model="users.barwala.username"
                            readonly
                          >
                        </div>
                        <div class="mb-3">
                          <label class="form-label fw-bold">New Password</label>
                          <div class="input-group">
                            <input 
                              :type="showPasswords.barwala ? 'text' : 'password'"
                              class="form-control" 
                              v-model="users.barwala.newPassword"
                              placeholder="Enter new password"
                            >
                            <button 
                              class="btn btn-outline-secondary" 
                              type="button"
                              @click="togglePasswordVisibility('barwala')"
                            >
                              <i class="fas" :class="showPasswords.barwala ? 'fa-eye-slash' : 'fa-eye'"></i>
                            </button>
                          </div>
                        </div>
                        <div class="d-flex gap-2">
                          <button 
                            class="btn btn-primary flex-fill"
                            @click="updatePassword('barwala')"
                            :disabled="!users.barwala.newPassword || updating.barwala"
                          >
                            <span v-if="updating.barwala" class="spinner-border spinner-border-sm me-2"></span>
                            <i v-else class="fas fa-key me-2"></i>
                            {{ updating.barwala ? 'Updating...' : 'Update Password' }}
                          </button>
                          <button 
                            class="btn btn-outline-secondary"
                            @click="generatePassword('barwala')"
                          >
                            <i class="fas fa-random"></i>
                          </button>
                        </div>
                        <div class="mt-2">
                          <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Last updated: {{ users.barwala.lastUpdated || 'Never' }}
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- CAD-1 Hisar -->
                  <div class="col-md-6">
                    <div class="card border border-success">
                      <div class="card-header bg-success text-white">
                        <h6 class="mb-0 fw-bold">
                          <i class="fas fa-map-marker-alt me-2"></i>
                          CAD-1 Hisar Subdivision
                        </h6>
                      </div>
                      <div class="card-body">
                        <div class="mb-3">
                          <label class="form-label fw-bold">Username</label>
                          <input 
                            type="text" 
                            class="form-control" 
                            v-model="users.cad1_hisar.username"
                            readonly
                          >
                        </div>
                        <div class="mb-3">
                          <label class="form-label fw-bold">New Password</label>
                          <div class="input-group">
                            <input 
                              :type="showPasswords.cad1_hisar ? 'text' : 'password'"
                              class="form-control" 
                              v-model="users.cad1_hisar.newPassword"
                              placeholder="Enter new password"
                            >
                            <button 
                              class="btn btn-outline-secondary" 
                              type="button"
                              @click="togglePasswordVisibility('cad1_hisar')"
                            >
                              <i class="fas" :class="showPasswords.cad1_hisar ? 'fa-eye-slash' : 'fa-eye'"></i>
                            </button>
                          </div>
                        </div>
                        <div class="d-flex gap-2">
                          <button 
                            class="btn btn-success flex-fill"
                            @click="updatePassword('cad1_hisar')"
                            :disabled="!users.cad1_hisar.newPassword || updating.cad1_hisar"
                          >
                            <span v-if="updating.cad1_hisar" class="spinner-border spinner-border-sm me-2"></span>
                            <i v-else class="fas fa-key me-2"></i>
                            {{ updating.cad1_hisar ? 'Updating...' : 'Update Password' }}
                          </button>
                          <button 
                            class="btn btn-outline-secondary"
                            @click="generatePassword('cad1_hisar')"
                          >
                            <i class="fas fa-random"></i>
                          </button>
                        </div>
                        <div class="mt-2">
                          <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Last updated: {{ users.cad1_hisar.lastUpdated || 'Never' }}
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- CAD-2 Hisar -->
                  <div class="col-md-6">
                    <div class="card border border-warning">
                      <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0 fw-bold">
                          <i class="fas fa-map-marker-alt me-2"></i>
                          CAD-2 Hisar Subdivision
                        </h6>
                      </div>
                      <div class="card-body">
                        <div class="mb-3">
                          <label class="form-label fw-bold">Username</label>
                          <input 
                            type="text" 
                            class="form-control" 
                            v-model="users.cad2_hisar.username"
                            readonly
                          >
                        </div>
                        <div class="mb-3">
                          <label class="form-label fw-bold">New Password</label>
                          <div class="input-group">
                            <input 
                              :type="showPasswords.cad2_hisar ? 'text' : 'password'"
                              class="form-control" 
                              v-model="users.cad2_hisar.newPassword"
                              placeholder="Enter new password"
                            >
                            <button 
                              class="btn btn-outline-secondary" 
                              type="button"
                              @click="togglePasswordVisibility('cad2_hisar')"
                            >
                              <i class="fas" :class="showPasswords.cad2_hisar ? 'fa-eye-slash' : 'fa-eye'"></i>
                            </button>
                          </div>
                        </div>
                        <div class="d-flex gap-2">
                          <button 
                            class="btn btn-warning flex-fill"
                            @click="updatePassword('cad2_hisar')"
                            :disabled="!users.cad2_hisar.newPassword || updating.cad2_hisar"
                          >
                            <span v-if="updating.cad2_hisar" class="spinner-border spinner-border-sm me-2"></span>
                            <i v-else class="fas fa-key me-2"></i>
                            {{ updating.cad2_hisar ? 'Updating...' : 'Update Password' }}
                          </button>
                          <button 
                            class="btn btn-outline-secondary"
                            @click="generatePassword('cad2_hisar')"
                          >
                            <i class="fas fa-random"></i>
                          </button>
                        </div>
                        <div class="mt-2">
                          <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Last updated: {{ users.cad2_hisar.lastUpdated || 'Never' }}
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Narwana -->
                  <div class="col-md-6">
                    <div class="card border border-info">
                      <div class="card-header bg-info text-white">
                        <h6 class="mb-0 fw-bold">
                          <i class="fas fa-map-marker-alt me-2"></i>
                          Narwana Subdivision
                        </h6>
                      </div>
                      <div class="card-body">
                        <div class="mb-3">
                          <label class="form-label fw-bold">Username</label>
                          <input 
                            type="text" 
                            class="form-control" 
                            v-model="users.narwana.username"
                            readonly
                          >
                        </div>
                        <div class="mb-3">
                          <label class="form-label fw-bold">New Password</label>
                          <div class="input-group">
                            <input 
                              :type="showPasswords.narwana ? 'text' : 'password'"
                              class="form-control" 
                              v-model="users.narwana.newPassword"
                              placeholder="Enter new password"
                            >
                            <button 
                              class="btn btn-outline-secondary" 
                              type="button"
                              @click="togglePasswordVisibility('narwana')"
                            >
                              <i class="fas" :class="showPasswords.narwana ? 'fa-eye-slash' : 'fa-eye'"></i>
                            </button>
                          </div>
                        </div>
                        <div class="d-flex gap-2">
                          <button 
                            class="btn btn-info flex-fill"
                            @click="updatePassword('narwana')"
                            :disabled="!users.narwana.newPassword || updating.narwana"
                          >
                            <span v-if="updating.narwana" class="spinner-border spinner-border-sm me-2"></span>
                            <i v-else class="fas fa-key me-2"></i>
                            {{ updating.narwana ? 'Updating...' : 'Update Password' }}
                          </button>
                          <button 
                            class="btn btn-outline-secondary"
                            @click="generatePassword('narwana')"
                          >
                            <i class="fas fa-random"></i>
                          </button>
                        </div>
                        <div class="mt-2">
                          <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Last updated: {{ users.narwana.lastUpdated || 'Never' }}
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Security Guidelines -->
        <div class="row mt-4">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-light">
                <h6 class="mb-0 fw-bold">
                  <i class="fas fa-shield-alt me-2"></i>
                  Security Guidelines
                </h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <h6 class="fw-bold text-success">
                      <i class="fas fa-check-circle me-2"></i>
                      Password Best Practices
                    </h6>
                    <ul class="list-unstyled">
                      <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Use at least 8 characters
                      </li>
                      <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Include uppercase and lowercase letters
                      </li>
                      <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Include numbers and special characters
                      </li>
                      <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Change passwords regularly
                      </li>
                    </ul>
                  </div>
                  <div class="col-md-6">
                    <h6 class="fw-bold text-warning">
                      <i class="fas fa-exclamation-triangle me-2"></i>
                      Security Reminders
                    </h6>
                    <ul class="list-unstyled">
                      <li class="mb-2">
                        <i class="fas fa-times text-danger me-2"></i>
                        Don't share passwords with others
                      </li>
                      <li class="mb-2">
                        <i class="fas fa-times text-danger me-2"></i>
                        Don't use common passwords
                      </li>
                      <li class="mb-2">
                        <i class="fas fa-times text-danger me-2"></i>
                        Don't write passwords down
                      </li>
                      <li class="mb-2">
                        <i class="fas fa-times text-danger me-2"></i>
                        Don't use the same password everywhere
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Toast -->
    <div 
      v-if="showSuccessToast" 
      class="toast-container position-fixed top-0 end-0 p-3"
      style="z-index: 1100;"
    >
      <div class="toast show" role="alert">
        <div class="toast-header bg-success text-white">
          <i class="fas fa-check-circle me-2"></i>
          <strong class="me-auto">Success</strong>
          <button type="button" class="btn-close btn-close-white" @click="showSuccessToast = false"></button>
        </div>
        <div class="toast-body">
          {{ successMessage }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Sidebar from '@/components/common/Sidebar.vue'

// Reactive data
const showSuccessToast = ref(false)
const successMessage = ref('')

const users = ref({
  barwala: {
    username: 'barwala_sdo',
    newPassword: '',
    lastUpdated: ''
  },
  cad1_hisar: {
    username: 'cad1_hisar_sdo',
    newPassword: '',
    lastUpdated: ''
  },
  cad2_hisar: {
    username: 'cad2_hisar_sdo',
    newPassword: '',
    lastUpdated: ''
  },
  narwana: {
    username: 'narwana_sdo',
    newPassword: '',
    lastUpdated: ''
  }
})

const showPasswords = ref({
  barwala: false,
  cad1_hisar: false,
  cad2_hisar: false,
  narwana: false
})

const updating = ref({
  barwala: false,
  cad1_hisar: false,
  cad2_hisar: false,
  narwana: false
})

// Methods
const togglePasswordVisibility = (subdivision: string) => {
  showPasswords.value[subdivision as keyof typeof showPasswords.value] =
    !showPasswords.value[subdivision as keyof typeof showPasswords.value]
}

const generatePassword = (subdivision: string) => {
  const length = 12
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
  let password = ''

  // Ensure at least one character from each category
  const lowercase = 'abcdefghijklmnopqrstuvwxyz'
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const numbers = '0123456789'
  const special = '!@#$%^&*'

  password += lowercase[Math.floor(Math.random() * lowercase.length)]
  password += uppercase[Math.floor(Math.random() * uppercase.length)]
  password += numbers[Math.floor(Math.random() * numbers.length)]
  password += special[Math.floor(Math.random() * special.length)]

  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += charset[Math.floor(Math.random() * charset.length)]
  }

  // Shuffle the password
  password = password.split('').sort(() => Math.random() - 0.5).join('')

  users.value[subdivision as keyof typeof users.value].newPassword = password
}

const updatePassword = async (subdivision: string) => {
  updating.value[subdivision as keyof typeof updating.value] = true

  try {
    // Simulate API call - in real implementation, this would call the Cloudflare Worker
    await new Promise(resolve => setTimeout(resolve, 1500))

    // Update last updated timestamp
    users.value[subdivision as keyof typeof users.value].lastUpdated =
      new Date().toLocaleString('en-IN')

    // Clear the password field
    users.value[subdivision as keyof typeof users.value].newPassword = ''

    // Show success message
    const subdivisionNames: Record<string, string> = {
      'barwala': 'Barwala',
      'cad1_hisar': 'CAD-1 Hisar',
      'cad2_hisar': 'CAD-2 Hisar',
      'narwana': 'Narwana'
    }

    successMessage.value = `Password updated successfully for ${subdivisionNames[subdivision]} subdivision`
    showSuccessToast.value = true

    // Auto-hide toast after 3 seconds
    setTimeout(() => {
      showSuccessToast.value = false
    }, 3000)

  } catch (error) {
    console.error('Error updating password:', error)
  } finally {
    updating.value[subdivision as keyof typeof updating.value] = false
  }
}
</script>

<style scoped>
.dashboard-layout {
  display: flex;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  margin-left: 280px;
  padding: 2rem;
  background-color: #f8f9fa;
  transition: margin-left 0.3s ease;
}

.card {
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.toast-container {
  z-index: 1100;
}

.toast {
  min-width: 300px;
}

.input-group .btn {
  border-left: none;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding: 1rem;
  }
}
</style>
