<script setup lang="ts">
import { RouterView } from 'vue-router'
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useDataStore } from '@/stores/data'

const authStore = useAuthStore()
const dataStore = useDataStore()

onMounted(() => {
  // Initialize authentication state from localStorage
  authStore.initializeAuth()

  // Initialize sample data
  dataStore.initializeSampleData()
})
</script>

<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: #2d3748;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0,0,0,0.05);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Beautiful card styles */
.card {
  border: none !important;
  border-radius: 20px !important;
  box-shadow: 0 10px 30px rgba(0,0,0,0.08) !important;
  backdrop-filter: blur(10px);
  background: rgba(255,255,255,0.95) !important;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
}

.card:hover {
  transform: translateY(-8px) !important;
  box-shadow: 0 20px 60px rgba(0,0,0,0.15) !important;
}

.card-header {
  border: none !important;
  border-radius: 20px 20px 0 0 !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  padding: 1.5rem !important;
}

.card-body {
  padding: 2rem !important;
}

/* Beautiful table styles */
.table {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0,0,0,0.08);
  background: white;
}

.table thead th {
  background: linear-gradient(135deg, #f8f9ff 0%, #e6e9ff 100%) !important;
  border: none !important;
  color: #4a5568 !important;
  font-weight: 600 !important;
  padding: 1.2rem 1rem !important;
  font-size: 0.875rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.table tbody tr {
  border: none !important;
  transition: all 0.3s ease !important;
}

.table tbody tr:hover {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%) !important;
  transform: scale(1.01) !important;
}

.table tbody td {
  border: none !important;
  padding: 1rem !important;
  vertical-align: middle !important;
}

/* Status styles with beautiful gradients */
.status-pending {
  background: linear-gradient(135deg, #fff5b7 0%, #ffeaa7 100%) !important;
  border-left: 4px solid #fdcb6e !important;
}

.status-in-progress {
  background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%) !important;
  border-left: 4px solid #00b894 !important;
}

.status-completed {
  background: linear-gradient(135deg, #b8e6b8 0%, #a8d8a8 100%) !important;
  border-left: 4px solid #00b894 !important;
}

.status-rejected {
  background: linear-gradient(135deg, #ffb3ba 0%, #ff9aa2 100%) !important;
  border-left: 4px solid #e17055 !important;
}

/* Beautiful button styles */
.btn {
  border-radius: 12px !important;
  font-weight: 500 !important;
  padding: 0.75rem 1.5rem !important;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
  border: none !important;
  font-size: 0.875rem !important;
  letter-spacing: 0.5px !important;
}

.btn-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6) !important;
  color: white !important;
}

.btn-primary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4) !important;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #43a3f5 0%, #00e8f5 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.6) !important;
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
  box-shadow: 0 4px 15px rgba(86, 171, 47, 0.4) !important;
}

.btn-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
  box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4) !important;
}

.btn-outline-primary {
  border: 2px solid #667eea !important;
  color: #667eea !important;
  background: transparent !important;
}

.btn-outline-primary:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

/* Beautiful badges */
.badge {
  border-radius: 20px !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
  font-size: 0.75rem !important;
  letter-spacing: 0.5px !important;
}

.bg-warning {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
  color: #8b4513 !important;
}

.bg-primary {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important;
  color: #2d3748 !important;
}

.bg-success {
  background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%) !important;
  color: #2d3748 !important;
}

.bg-danger {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
  color: #8b4513 !important;
}

/* Beautiful form controls */
.form-control, .form-select {
  border: 2px solid #e2e8f0 !important;
  border-radius: 12px !important;
  padding: 0.75rem 1rem !important;
  transition: all 0.3s ease !important;
  background: rgba(255,255,255,0.9) !important;
  font-size: 0.875rem !important;
}

.form-control:focus, .form-select:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
  background: white !important;
}

.input-group-text {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%) !important;
  border: 2px solid #e2e8f0 !important;
  border-right: none !important;
  border-radius: 12px 0 0 12px !important;
  color: #667eea !important;
}

/* Beautiful progress bars */
.progress {
  height: 8px !important;
  border-radius: 20px !important;
  background: rgba(0,0,0,0.05) !important;
  overflow: hidden !important;
}

.progress-bar {
  border-radius: 20px !important;
  transition: all 0.3s ease !important;
}

.bg-success.progress-bar {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
}

.bg-info.progress-bar {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.bg-warning.progress-bar {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.bg-danger.progress-bar {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
}

/* Beautiful modal */
.modal-content {
  border: none !important;
  border-radius: 20px !important;
  box-shadow: 0 20px 60px rgba(0,0,0,0.2) !important;
  backdrop-filter: blur(10px) !important;
}

.modal-header {
  border: none !important;
  border-radius: 20px 20px 0 0 !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
}

.modal-backdrop {
  background: rgba(0,0,0,0.6) !important;
  backdrop-filter: blur(5px) !important;
}

/* Beautiful pagination */
.pagination .page-link {
  border: none !important;
  border-radius: 10px !important;
  margin: 0 2px !important;
  color: #667eea !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.pagination .page-item.active .page-link {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
}

.pagination .page-link:hover {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%) !important;
  color: #667eea !important;
  transform: translateY(-2px) !important;
}

/* Loading spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Beautiful animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card, .table, .btn {
  animation: fadeInUp 0.6s ease-out;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .card-body {
    padding: 1.5rem !important;
  }

  .btn {
    padding: 0.6rem 1.2rem !important;
    font-size: 0.8rem !important;
  }
}
</style>
