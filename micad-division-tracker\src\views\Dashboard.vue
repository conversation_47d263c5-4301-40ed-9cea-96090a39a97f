<template>
  <div class="dashboard-layout">
    <Sidebar />
    
    <div class="main-content">
      <div class="container-fluid">
        <!-- Header -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h2 class="fw-bold mb-1">
                  <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                  Dashboard
                </h2>
                <p class="text-muted mb-0">
                  {{ authStore.isAdmin ? 'Executive Engineer - MICAD Division Hisar' : `SDO - ${formatSubdivision(authStore.userSubdivision)}` }}
                </p>
              </div>
              <div class="text-end">
                <div class="text-muted small">Last Updated</div>
                <div class="fw-bold">{{ currentDateTime }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- KPI Cards -->
        <KPICards />

        <!-- Data Tables -->
        <div class="row">
          <div class="col-12">
            <div class="card border-0 shadow-sm">
              <div class="card-header bg-white border-bottom">
                <ul class="nav nav-tabs card-header-tabs" role="tablist">
                  <li class="nav-item" role="presentation">
                    <button 
                      class="nav-link active" 
                      id="applications-tab" 
                      data-bs-toggle="tab" 
                      data-bs-target="#applications" 
                      type="button" 
                      role="tab"
                    >
                      <i class="fas fa-file-alt me-2"></i>
                      New Applications
                      <span class="badge bg-warning ms-2">{{ filteredApplications.length }}</span>
                    </button>
                  </li>
                  <li class="nav-item" role="presentation">
                    <button 
                      class="nav-link" 
                      id="works-tab" 
                      data-bs-toggle="tab" 
                      data-bs-target="#works" 
                      type="button" 
                      role="tab"
                    >
                      <i class="fas fa-tools me-2"></i>
                      Status of Works
                      <span class="badge bg-primary ms-2">{{ filteredWorks.length }}</span>
                    </button>
                  </li>
                </ul>
              </div>

              <div class="card-body">
                <!-- Search and Filter Bar -->
                <div class="row mb-4">
                  <div class="col-md-6">
                    <div class="input-group">
                      <span class="input-group-text">
                        <i class="fas fa-search"></i>
                      </span>
                      <input
                        type="text"
                        class="form-control"
                        placeholder="Search by water course, farmer name, village..."
                        v-model="searchQuery"
                      />
                    </div>
                  </div>
                  <div class="col-md-3" v-if="authStore.isAdmin">
                    <select class="form-select" v-model="selectedSubdivision">
                      <option value="">All Subdivisions</option>
                      <option value="barwala">Barwala</option>
                      <option value="cad1_hisar">CAD-1 Hisar</option>
                      <option value="cad2_hisar">CAD-2 Hisar</option>
                      <option value="narwana">Narwana</option>
                    </select>
                  </div>
                  <div class="col-md-3">
                    <button class="btn btn-outline-secondary" @click="clearFilters">
                      <i class="fas fa-times me-2"></i>
                      Clear Filters
                    </button>
                  </div>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                  <!-- Applications Tab -->
                  <div class="tab-pane fade show active" id="applications" role="tabpanel">
                    <div class="table-responsive">
                      <table class="table table-hover">
                        <thead class="table-light">
                          <tr>
                            <th>Sr. No.</th>
                            <th>RD</th>
                            <th>Water Course</th>
                            <th>Village</th>
                            <th>Farmer Name</th>
                            <th>Status</th>
                            <th v-if="authStore.isAdmin">Subdivision</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr 
                            v-for="application in paginatedApplications" 
                            :key="application.id"
                            :class="getStatusClass(application.status)"
                          >
                            <td>{{ application.sr_no }}</td>
                            <td>{{ application.rd }}</td>
                            <td class="fw-bold">{{ application.minor }}</td>
                            <td>{{ application.village }}</td>
                            <td>{{ application.farmer_name }}</td>
                            <td>
                              <span class="badge" :class="getStatusBadgeClass(application.status)">
                                {{ application.status }}
                              </span>
                            </td>
                            <td v-if="authStore.isAdmin">
                              <span class="badge bg-secondary">{{ formatSubdivision(application.subdivision) }}</span>
                            </td>
                            <td>
                              <button class="btn btn-sm btn-outline-primary me-1" title="View Details">
                                <i class="fas fa-eye"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-success" title="Add ATR">
                                <i class="fas fa-edit"></i>
                              </button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <!-- Pagination for Applications -->
                    <nav v-if="totalApplicationsPages > 1">
                      <ul class="pagination justify-content-center">
                        <li class="page-item" :class="{ disabled: currentApplicationsPage === 1 }">
                          <button class="page-link" @click="currentApplicationsPage = 1">First</button>
                        </li>
                        <li class="page-item" :class="{ disabled: currentApplicationsPage === 1 }">
                          <button class="page-link" @click="currentApplicationsPage--">Previous</button>
                        </li>
                        <li 
                          v-for="page in getPageNumbers(totalApplicationsPages, currentApplicationsPage)" 
                          :key="page"
                          class="page-item" 
                          :class="{ active: page === currentApplicationsPage }"
                        >
                          <button class="page-link" @click="currentApplicationsPage = page">{{ page }}</button>
                        </li>
                        <li class="page-item" :class="{ disabled: currentApplicationsPage === totalApplicationsPages }">
                          <button class="page-link" @click="currentApplicationsPage++">Next</button>
                        </li>
                        <li class="page-item" :class="{ disabled: currentApplicationsPage === totalApplicationsPages }">
                          <button class="page-link" @click="currentApplicationsPage = totalApplicationsPages">Last</button>
                        </li>
                      </ul>
                    </nav>
                  </div>

                  <!-- Works Tab -->
                  <div class="tab-pane fade" id="works" role="tabpanel">
                    <div class="table-responsive">
                      <table class="table table-hover">
                        <thead class="table-light">
                          <tr>
                            <th>Water Course</th>
                            <th>Work Name</th>
                            <th>AA No. & Date</th>
                            <th>Contractor</th>
                            <th>Physical Progress</th>
                            <th>Financial Expenditure</th>
                            <th v-if="authStore.isAdmin">Subdivision</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="work in paginatedWorks" :key="work.id">
                            <td class="fw-bold">{{ work.water_course }}</td>
                            <td>{{ work.work_name }}</td>
                            <td>{{ work.aa_no_date }}</td>
                            <td>{{ work.contractor }}</td>
                            <td>
                              <div class="progress" style="height: 20px;">
                                <div 
                                  class="progress-bar" 
                                  :class="getProgressBarClass(work.physical_status_percent)"
                                  :style="{ width: work.physical_status_percent + '%' }"
                                >
                                  {{ work.physical_status_percent }}%
                                </div>
                              </div>
                            </td>
                            <td>₹{{ formatCurrency(work.financial_expenditure) }}</td>
                            <td v-if="authStore.isAdmin">
                              <span class="badge bg-secondary">{{ formatSubdivision(work.subdivision) }}</span>
                            </td>
                            <td>
                              <button class="btn btn-sm btn-outline-primary me-1" title="View Details">
                                <i class="fas fa-eye"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-success" title="Update Progress">
                                <i class="fas fa-edit"></i>
                              </button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <!-- Pagination for Works -->
                    <nav v-if="totalWorksPages > 1">
                      <ul class="pagination justify-content-center">
                        <li class="page-item" :class="{ disabled: currentWorksPage === 1 }">
                          <button class="page-link" @click="currentWorksPage = 1">First</button>
                        </li>
                        <li class="page-item" :class="{ disabled: currentWorksPage === 1 }">
                          <button class="page-link" @click="currentWorksPage--">Previous</button>
                        </li>
                        <li 
                          v-for="page in getPageNumbers(totalWorksPages, currentWorksPage)" 
                          :key="page"
                          class="page-item" 
                          :class="{ active: page === currentWorksPage }"
                        >
                          <button class="page-link" @click="currentWorksPage = page">{{ page }}</button>
                        </li>
                        <li class="page-item" :class="{ disabled: currentWorksPage === totalWorksPages }">
                          <button class="page-link" @click="currentWorksPage++">Next</button>
                        </li>
                        <li class="page-item" :class="{ disabled: currentWorksPage === totalWorksPages }">
                          <button class="page-link" @click="currentWorksPage = totalWorksPages">Last</button>
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useDataStore } from '@/stores/data'
import Sidebar from '@/components/common/Sidebar.vue'
import KPICards from '@/components/dashboard/KPICards.vue'

const authStore = useAuthStore()
const dataStore = useDataStore()

// Reactive data
const searchQuery = ref('')
const selectedSubdivision = ref('')
const currentApplicationsPage = ref(1)
const currentWorksPage = ref(1)
const itemsPerPage = 10
const currentDateTime = ref('')

// Computed properties
const filteredApplications = computed(() => {
  let applications = authStore.isAdmin
    ? dataStore.applications
    : dataStore.getApplicationsBySubdivision(authStore.userSubdivision)

  // Apply subdivision filter for admin
  if (authStore.isAdmin && selectedSubdivision.value) {
    applications = applications.filter(app => app.subdivision === selectedSubdivision.value)
  }

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    applications = applications.filter(app =>
      app.minor.toLowerCase().includes(query) ||
      app.farmer_name.toLowerCase().includes(query) ||
      app.village.toLowerCase().includes(query) ||
      app.status.toLowerCase().includes(query)
    )
  }

  return applications
})

const filteredWorks = computed(() => {
  let works = authStore.isAdmin
    ? dataStore.works
    : dataStore.getWorksBySubdivision(authStore.userSubdivision)

  // Apply subdivision filter for admin
  if (authStore.isAdmin && selectedSubdivision.value) {
    works = works.filter(work => work.subdivision === selectedSubdivision.value)
  }

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    works = works.filter(work =>
      work.water_course.toLowerCase().includes(query) ||
      work.work_name.toLowerCase().includes(query) ||
      work.contractor.toLowerCase().includes(query)
    )
  }

  return works
})

const totalApplicationsPages = computed(() =>
  Math.ceil(filteredApplications.value.length / itemsPerPage)
)

const totalWorksPages = computed(() =>
  Math.ceil(filteredWorks.value.length / itemsPerPage)
)

const paginatedApplications = computed(() => {
  const start = (currentApplicationsPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredApplications.value.slice(start, end)
})

const paginatedWorks = computed(() => {
  const start = (currentWorksPage.value - 1) * itemsPerPage
  const end = start + itemsPerPage
  return filteredWorks.value.slice(start, end)
})

// Methods
const formatSubdivision = (subdivision?: string) => {
  if (!subdivision) return ''

  const subdivisionNames: Record<string, string> = {
    'barwala': 'Barwala',
    'cad1_hisar': 'CAD-1 Hisar',
    'cad2_hisar': 'CAD-2 Hisar',
    'narwana': 'Narwana'
  }

  return subdivisionNames[subdivision] || subdivision
}

const getStatusClass = (status: string) => {
  const statusClasses: Record<string, string> = {
    'Pending': 'status-pending',
    'In Progress': 'status-in-progress',
    'Completed': 'status-completed',
    'Rejected': 'status-rejected'
  }
  return statusClasses[status] || ''
}

const getStatusBadgeClass = (status: string) => {
  const badgeClasses: Record<string, string> = {
    'Pending': 'bg-warning',
    'In Progress': 'bg-primary',
    'Completed': 'bg-success',
    'Rejected': 'bg-danger'
  }
  return badgeClasses[status] || 'bg-secondary'
}

const getProgressBarClass = (percentage: number) => {
  if (percentage >= 100) return 'bg-success'
  if (percentage >= 75) return 'bg-info'
  if (percentage >= 50) return 'bg-warning'
  return 'bg-danger'
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN').format(amount)
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedSubdivision.value = ''
  currentApplicationsPage.value = 1
  currentWorksPage.value = 1
}

const getPageNumbers = (totalPages: number, currentPage: number) => {
  const pages = []
  const maxVisible = 5

  let start = Math.max(1, currentPage - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages, start + maxVisible - 1)

  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
}

const updateDateTime = () => {
  const now = new Date()
  currentDateTime.value = now.toLocaleString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

onMounted(() => {
  updateDateTime()
  setInterval(updateDateTime, 60000) // Update every minute
})
</script>

<style scoped>
.dashboard-layout {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.main-content {
  flex: 1;
  margin-left: 280px;
  padding: 2rem;
  background: transparent;
  transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.main-content::before {
  content: '';
  position: fixed;
  top: 0;
  left: 280px;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  z-index: -1;
}

.nav-tabs .nav-link {
  border: none;
  color: #6c757d;
  font-weight: 500;
}

.nav-tabs .nav-link.active {
  background-color: transparent;
  border-bottom: 3px solid #0d6efd;
  color: #0d6efd;
  font-weight: 600;
}

.table th {
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.progress {
  border-radius: 10px;
}

.progress-bar {
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 600;
}

.pagination .page-link {
  border: none;
  color: #6c757d;
  font-weight: 500;
}

.pagination .page-item.active .page-link {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.pagination .page-link:hover {
  background-color: #e9ecef;
  color: #0d6efd;
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding: 1rem;
  }
}
</style>
