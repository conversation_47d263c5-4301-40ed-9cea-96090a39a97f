<template>
  <div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
      <div class="card kpi-card card-hover border-0 shadow-sm">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="kpi-icon bg-warning bg-gradient">
              <i class="fas fa-clock text-white"></i>
            </div>
            <div class="ms-3">
              <h3 class="mb-0 fw-bold text-warning">{{ dataStore.totalPendingApplications }}</h3>
              <p class="text-muted mb-0 small">Pending Applications</p>
            </div>
          </div>
          <div class="mt-3">
            <small class="text-muted">
              <i class="fas fa-info-circle me-1"></i>
              Requires immediate attention
            </small>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6">
      <div class="card kpi-card card-hover border-0 shadow-sm">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="kpi-icon bg-primary bg-gradient">
              <i class="fas fa-tools text-white"></i>
            </div>
            <div class="ms-3">
              <h3 class="mb-0 fw-bold text-primary">{{ dataStore.totalWorksInProgress }}</h3>
              <p class="text-muted mb-0 small">Works in Progress</p>
            </div>
          </div>
          <div class="mt-3">
            <small class="text-muted">
              <i class="fas fa-chart-line me-1"></i>
              Active construction projects
            </small>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6">
      <div class="card kpi-card card-hover border-0 shadow-sm">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="kpi-icon bg-info bg-gradient">
              <i class="fas fa-calendar-alt text-white"></i>
            </div>
            <div class="ms-3">
              <h3 class="mb-0 fw-bold text-info">{{ dataStore.totalApplicationsLast30Days }}</h3>
              <p class="text-muted mb-0 small">New Applications (30 days)</p>
            </div>
          </div>
          <div class="mt-3">
            <small class="text-muted">
              <i class="fas fa-trending-up me-1"></i>
              Recent submissions
            </small>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6">
      <div class="card kpi-card card-hover border-0 shadow-sm">
        <div class="card-body">
          <div class="d-flex align-items-center">
            <div class="kpi-icon bg-success bg-gradient">
              <i class="fas fa-check-circle text-white"></i>
            </div>
            <div class="ms-3">
              <h3 class="mb-0 fw-bold text-success">{{ dataStore.totalWorksCompleted }}</h3>
              <p class="text-muted mb-0 small">Works Completed</p>
            </div>
          </div>
          <div class="mt-3">
            <small class="text-muted">
              <i class="fas fa-trophy me-1"></i>
              Successfully finished
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Additional Statistics Row -->
  <div class="row g-4 mb-4" v-if="authStore.isAdmin">
    <div class="col-md-6">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
          <h6 class="mb-0 fw-bold">
            <i class="fas fa-chart-pie me-2"></i>
            Applications by Status
          </h6>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-3">
              <div class="text-warning fw-bold h5">{{ getApplicationsByStatus('Pending') }}</div>
              <small class="text-muted">Pending</small>
            </div>
            <div class="col-3">
              <div class="text-primary fw-bold h5">{{ getApplicationsByStatus('In Progress') }}</div>
              <small class="text-muted">In Progress</small>
            </div>
            <div class="col-3">
              <div class="text-success fw-bold h5">{{ getApplicationsByStatus('Completed') }}</div>
              <small class="text-muted">Completed</small>
            </div>
            <div class="col-3">
              <div class="text-danger fw-bold h5">{{ getApplicationsByStatus('Rejected') }}</div>
              <small class="text-muted">Rejected</small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
          <h6 class="mb-0 fw-bold">
            <i class="fas fa-building me-2"></i>
            Subdivision Overview
          </h6>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-3">
              <div class="text-primary fw-bold h6">{{ getSubdivisionCount('barwala') }}</div>
              <small class="text-muted">Barwala</small>
            </div>
            <div class="col-3">
              <div class="text-primary fw-bold h6">{{ getSubdivisionCount('cad1_hisar') }}</div>
              <small class="text-muted">CAD-1</small>
            </div>
            <div class="col-3">
              <div class="text-primary fw-bold h6">{{ getSubdivisionCount('cad2_hisar') }}</div>
              <small class="text-muted">CAD-2</small>
            </div>
            <div class="col-3">
              <div class="text-primary fw-bold h6">{{ getSubdivisionCount('narwana') }}</div>
              <small class="text-muted">Narwana</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDataStore } from '@/stores/data'
import { useAuthStore } from '@/stores/auth'

const dataStore = useDataStore()
const authStore = useAuthStore()

const getApplicationsByStatus = (status: string) => {
  return dataStore.applications.filter(app => app.status === status).length
}

const getSubdivisionCount = (subdivision: string) => {
  return dataStore.applications.filter(app => app.subdivision === subdivision).length
}
</script>

<style scoped>
.kpi-card {
  border-left: 4px solid transparent;
  transition: all 0.3s ease;
}

.kpi-card:hover {
  border-left-color: var(--bs-primary);
}

.kpi-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.bg-gradient {
  background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary) 100%);
}

.bg-warning.bg-gradient {
  background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
}

.bg-info.bg-gradient {
  background: linear-gradient(135deg, #0dcaf0 0%, #0056b3 100%);
}

.bg-success.bg-gradient {
  background: linear-gradient(135deg, #198754 0%, #0f5132 100%);
}

.bg-primary.bg-gradient {
  background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
}
</style>
