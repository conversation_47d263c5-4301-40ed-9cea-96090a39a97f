<template>
  <div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
      <div class="kpi-card pending-card">
        <div class="kpi-background">
          <div class="kpi-pattern"></div>
        </div>
        <div class="kpi-content">
          <div class="kpi-header">
            <div class="kpi-icon-container pending">
              <i class="fas fa-clock"></i>
            </div>
            <div class="kpi-trend">
              <i class="fas fa-arrow-up"></i>
            </div>
          </div>
          <div class="kpi-body">
            <h2 class="kpi-number">{{ dataStore.totalPendingApplications }}</h2>
            <p class="kpi-label">Pending Applications</p>
            <div class="kpi-description">
              <i class="fas fa-exclamation-circle me-1"></i>
              Requires immediate attention
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6">
      <div class="kpi-card progress-card">
        <div class="kpi-background">
          <div class="kpi-pattern"></div>
        </div>
        <div class="kpi-content">
          <div class="kpi-header">
            <div class="kpi-icon-container progress">
              <i class="fas fa-tools"></i>
            </div>
            <div class="kpi-trend">
              <i class="fas fa-arrow-up"></i>
            </div>
          </div>
          <div class="kpi-body">
            <h2 class="kpi-number">{{ dataStore.totalWorksInProgress }}</h2>
            <p class="kpi-label">Works in Progress</p>
            <div class="kpi-description">
              <i class="fas fa-chart-line me-1"></i>
              Active construction projects
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6">
      <div class="kpi-card recent-card">
        <div class="kpi-background">
          <div class="kpi-pattern"></div>
        </div>
        <div class="kpi-content">
          <div class="kpi-header">
            <div class="kpi-icon-container recent">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="kpi-trend">
              <i class="fas fa-arrow-up"></i>
            </div>
          </div>
          <div class="kpi-body">
            <h2 class="kpi-number">{{ dataStore.totalApplicationsLast30Days }}</h2>
            <p class="kpi-label">New Applications (30 days)</p>
            <div class="kpi-description">
              <i class="fas fa-trending-up me-1"></i>
              Recent submissions
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-xl-3 col-md-6">
      <div class="kpi-card completed-card">
        <div class="kpi-background">
          <div class="kpi-pattern"></div>
        </div>
        <div class="kpi-content">
          <div class="kpi-header">
            <div class="kpi-icon-container completed">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="kpi-trend">
              <i class="fas fa-arrow-up"></i>
            </div>
          </div>
          <div class="kpi-body">
            <h2 class="kpi-number">{{ dataStore.totalWorksCompleted }}</h2>
            <p class="kpi-label">Works Completed</p>
            <div class="kpi-description">
              <i class="fas fa-trophy me-1"></i>
              Successfully finished
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Additional Statistics Row -->
  <div class="row g-4 mb-4" v-if="authStore.isAdmin">
    <div class="col-md-6">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
          <h6 class="mb-0 fw-bold">
            <i class="fas fa-chart-pie me-2"></i>
            Applications by Status
          </h6>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-3">
              <div class="text-warning fw-bold h5">{{ getApplicationsByStatus('Pending') }}</div>
              <small class="text-muted">Pending</small>
            </div>
            <div class="col-3">
              <div class="text-primary fw-bold h5">{{ getApplicationsByStatus('In Progress') }}</div>
              <small class="text-muted">In Progress</small>
            </div>
            <div class="col-3">
              <div class="text-success fw-bold h5">{{ getApplicationsByStatus('Completed') }}</div>
              <small class="text-muted">Completed</small>
            </div>
            <div class="col-3">
              <div class="text-danger fw-bold h5">{{ getApplicationsByStatus('Rejected') }}</div>
              <small class="text-muted">Rejected</small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-6">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
          <h6 class="mb-0 fw-bold">
            <i class="fas fa-building me-2"></i>
            Subdivision Overview
          </h6>
        </div>
        <div class="card-body">
          <div class="row text-center">
            <div class="col-3">
              <div class="text-primary fw-bold h6">{{ getSubdivisionCount('barwala') }}</div>
              <small class="text-muted">Barwala</small>
            </div>
            <div class="col-3">
              <div class="text-primary fw-bold h6">{{ getSubdivisionCount('cad1_hisar') }}</div>
              <small class="text-muted">CAD-1</small>
            </div>
            <div class="col-3">
              <div class="text-primary fw-bold h6">{{ getSubdivisionCount('cad2_hisar') }}</div>
              <small class="text-muted">CAD-2</small>
            </div>
            <div class="col-3">
              <div class="text-primary fw-bold h6">{{ getSubdivisionCount('narwana') }}</div>
              <small class="text-muted">Narwana</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDataStore } from '@/stores/data'
import { useAuthStore } from '@/stores/auth'

const dataStore = useDataStore()
const authStore = useAuthStore()

const getApplicationsByStatus = (status: string) => {
  return dataStore.applications.filter(app => app.status === status).length
}

const getSubdivisionCount = (subdivision: string) => {
  return dataStore.applications.filter(app => app.subdivision === subdivision).length
}
</script>

<style scoped>
.kpi-card {
  position: relative;
  border-radius: 25px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  height: 180px;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.kpi-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.kpi-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.kpi-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  background-image: radial-gradient(circle at 20% 80%, rgba(255,255,255,0.2) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255,255,255,0.2) 0%, transparent 50%);
}

.kpi-content {
  position: relative;
  z-index: 2;
  padding: 1.5rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.kpi-icon-container {
  width: 60px;
  height: 60px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.kpi-icon-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, transparent 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.kpi-card:hover .kpi-icon-container::before {
  transform: translateX(100%);
}

.kpi-trend {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
  backdrop-filter: blur(10px);
}

.kpi-body {
  flex: 1;
}

.kpi-number {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0;
  color: white;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  line-height: 1;
}

.kpi-label {
  font-size: 1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.kpi-description {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* Card Specific Styles */
.pending-card {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

.pending-card .kpi-icon-container {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.progress-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.progress-card .kpi-icon-container {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.recent-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.recent-card .kpi-icon-container {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.completed-card {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.completed-card .kpi-icon-container {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  box-shadow: 0 8px 25px rgba(86, 171, 47, 0.4);
}

.completed-card .kpi-number,
.completed-card .kpi-label,
.completed-card .kpi-description {
  color: #2d3748;
}

.completed-card .kpi-trend {
  background: rgba(45, 55, 72, 0.2);
  color: #2d3748;
}

/* Animation */
.kpi-card {
  animation: slideInUp 0.6s ease-out;
}

.kpi-card:nth-child(1) { animation-delay: 0.1s; }
.kpi-card:nth-child(2) { animation-delay: 0.2s; }
.kpi-card:nth-child(3) { animation-delay: 0.3s; }
.kpi-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .kpi-card {
    height: 160px;
  }

  .kpi-content {
    padding: 1.25rem;
  }

  .kpi-number {
    font-size: 2rem;
  }

  .kpi-icon-container {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
}
</style>
